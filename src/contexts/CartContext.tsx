'use client';

import {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
  useCallback
} from 'react';
import { MenuItem, OrderStatus, PaymentMethod, DeliveryZoneType, Offer, AppliedDiscount } from '@/types/models';
import { DeliveryOption, DeliveryType } from '@/types/delivery';
import { useAuth } from '@/hooks/useAuth';
import {
  createOrder,
  getActiveOffers,
  validateOfferConditions,
  calculateDiscountAmount,
  incrementOfferUsage,
  getUserProfile
} from '@/lib/firebase/firestore';
import { useRouter } from 'next/navigation';
import { toast } from '@/hooks/use-toast';
import { useLocale } from './LocaleContext';

// Define the cart item type which extends MenuItem with quantity
export interface CartItem extends Omit<MenuItem, 'isFeatured' | 'isActive'> {
  quantity: number;
}

// Cart context type
interface CartContextType {
  cartItems: CartItem[];
  addToCart: (item: MenuItem, quantity: number) => void;
  updateQuantity: (itemId: string, quantity: number) => void;
  removeFromCart: (itemId: string) => void;
  clearCart: () => void;
  placeOrder: () => Promise<Order | null>;
  isCartEmpty: boolean;
  cartTotal: number;
  cartItemsCount: number;
  deliveryOption: DeliveryOption | null;
  setDeliveryOption: (option: DeliveryOption) => void;
  totalWithDelivery: number;
  // Delivery availability validation
  hasNonDeliveryItems: boolean;
  nonDeliveryItems: CartItem[];
  canSelectDelivery: boolean;
  validateDeliveryAvailability: () => { isValid: boolean; message?: string };
  // Discount functionality
  appliedDiscounts: AppliedDiscount[];
  availableOffers: Offer[];
  applyOffer: (offerId: string) => Promise<boolean>;
  removeDiscount: (offerId: string) => void;
  totalDiscount: number;
  finalTotal: number; // cartTotal - totalDiscount + deliveryFee
  refreshOffers: () => Promise<void>;
}

// Create the cart context
const CartContext = createContext<CartContextType | undefined>(undefined);

// Custom hook to use the cart context
export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

const CART_STORAGE_KEY = 'barcode-cafe-cart';

interface CartProviderProps {
  children: ReactNode;
}

// Cart Provider Component
export function CartProvider({ children }: CartProviderProps) {
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [deliveryOption, setDeliveryOption] = useState<DeliveryOption | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [appliedDiscounts, setAppliedDiscounts] = useState<AppliedDiscount[]>([]);
  const [availableOffers, setAvailableOffers] = useState<Offer[]>([]);
  const [userProfile, setUserProfile] = useState<any>(null);
  const { user } = useAuth();
  const router = useRouter();
  const { t } = useLocale();

  // Load cart items and delivery option from localStorage on initial mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedCart = localStorage.getItem(CART_STORAGE_KEY);
      if (savedCart) {
        try {
          const parsedCart = JSON.parse(savedCart);
          setCartItems(parsedCart);
        } catch (error) {
          console.error('Failed to parse cart from localStorage:', error);
        }
      }

      const savedDeliveryOption = localStorage.getItem(`${CART_STORAGE_KEY}-delivery`);
      if (savedDeliveryOption) {
        try {
          const parsedOption = JSON.parse(savedDeliveryOption);
          setDeliveryOption(parsedOption);
        } catch (error) {
          console.error('Failed to parse delivery option from localStorage:', error);
        }
      }
      
      setIsInitialized(true);
    }
  }, []);

  // Save cart to localStorage whenever it changes
  useEffect(() => {
    if (isInitialized && typeof window !== 'undefined') {
      localStorage.setItem(CART_STORAGE_KEY, JSON.stringify(cartItems));
    }
  }, [cartItems, isInitialized]);

  // Save delivery option to localStorage whenever it changes
  useEffect(() => {
    if (isInitialized && typeof window !== 'undefined' && deliveryOption) {
      localStorage.setItem(`${CART_STORAGE_KEY}-delivery`, JSON.stringify(deliveryOption));
    }
  }, [deliveryOption, isInitialized]);

  // Add an item to the cart
  const addToCart = (item: MenuItem, quantity: number) => {
    setCartItems(prevItems => {
      // Check if the item is already in the cart
      const existingItemIndex = prevItems.findIndex(cartItem => cartItem.id === item.id);
      
      if (existingItemIndex >= 0) {
        // Item exists, update its quantity
        const updatedItems = [...prevItems];
        updatedItems[existingItemIndex].quantity += quantity;
        return updatedItems;
      } else {
        // Item doesn't exist, add it with the specified quantity
        return [...prevItems, {
          ...item,
          quantity
        }];
      }
    });
  };

  // Update quantity of an item in the cart
  const updateQuantity = (itemId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(itemId);
      return;
    }
    
    setCartItems(prevItems => 
      prevItems.map(item => 
        item.id === itemId ? { ...item, quantity } : item
      )
    );
  };

  // Remove an item from the cart
  const removeFromCart = (itemId: string) => {
    setCartItems(prevItems => prevItems.filter(item => item.id !== itemId));
  };

  // Clear the cart
  const clearCart = () => {
    setCartItems([]);
    setDeliveryOption(null);
    setAppliedDiscounts([]);
    if (typeof window !== 'undefined') {
      localStorage.removeItem(`${CART_STORAGE_KEY}-delivery`);
    }
  };

  // Refresh available offers
  const refreshOffers = useCallback(async () => {
    try {
      const offers = await getActiveOffers();
      setAvailableOffers(offers);
    } catch (error) {
      console.error('Error fetching offers:', error);
    }
  }, []);

  // Apply an offer to the cart
  const applyOffer = async (offerId: string): Promise<boolean> => {
    try {
      const offer = availableOffers.find(o => o.id === offerId);
      if (!offer) {
        toast({
          title: t('menu.offers.notFound'),
          description: t('menu.offers.notFoundDesc'),
          variant: 'destructive',
        });
        return false;
      }

      // Check if offer is already applied
      if (appliedDiscounts.some(d => d.offerId === offerId)) {
        toast({
          title: t('menu.offers.alreadyApplied'),
          description: t('menu.offers.alreadyAppliedDesc'),
          variant: 'destructive',
        });
        return false;
      }

      // Validate offer conditions
      const cartItemsForValidation = cartItems.map(item => ({
        id: item.id,
        categoryId: item.categoryId,
        price: item.price,
        quantity: item.quantity,
        isAvailableForDelivery: item.isAvailableForDelivery
      }));

      const validation = validateOfferConditions(
        offer,
        cartItemsForValidation,
        deliveryOption?.type === DeliveryType.DELIVERY ? DeliveryZoneType.DELIVERY :
        deliveryOption?.type === DeliveryType.TABLE ? DeliveryZoneType.IN_HOUSE_TABLES :
        DeliveryZoneType.PICK_UP,
        userProfile?.loyaltyPoints || 0
      );

      if (!validation.isValid) {
        // Use specific error messages for package deals
        let title = t('menu.offers.notApplicable');
        let description = validation.reason || t('menu.offers.notApplicableDesc');

        if (validation.reason?.includes('package items')) {
          title = t('menu.offers.packageItemsRequired');
          description = t('menu.offers.packageItemsRequiredDesc');
        } else if (validation.reason?.includes('Insufficient quantity')) {
          title = t('menu.offers.insufficientQuantity');
          description = t('menu.offers.insufficientQuantityDesc');
        }

        toast({
          title,
          description,
          variant: 'destructive',
        });
        return false;
      }

      // Calculate discount amount
      const discountAmount = calculateDiscountAmount(
        offer,
        cartItemsForValidation,
        deliveryOption?.fee || 0
      );

      if (discountAmount <= 0) {
        toast({
          title: t('menu.offers.noDiscountApplicable'),
          description: t('menu.offers.noDiscountApplicableDesc'),
          variant: 'destructive',
        });
        return false;
      }

      // Create applied discount
      const appliedDiscount: AppliedDiscount = {
        offerId: offer.id,
        offerName: offer.name,
        offerType: offer.type,
        discountType: offer.discountType,
        discountAmount,
        appliedAt: new Date(),
      };

      setAppliedDiscounts(prev => [...prev, appliedDiscount]);

      toast({
        title: t('packages.offers.applied'),
        description: t('packages.offers.appliedDesc', {
          amount: discountAmount.toFixed(2),
          offerName: offer.name
        }),
        variant: 'success',
      });

      return true;
    } catch (error) {
      console.error('Error applying offer:', error);
      toast({
        title: t('menu.offers.errorApplying'),
        description: t('menu.offers.errorApplyingDesc'),
        variant: 'destructive',
      });
      return false;
    }
  };

  // Remove a discount from the cart
  const removeDiscount = (offerId: string) => {
    const discount = appliedDiscounts.find(d => d.offerId === offerId);
    if (discount) {
      setAppliedDiscounts(prev => prev.filter(d => d.offerId !== offerId));
      toast({
        title: t('menu.offers.removed'),
        description: t('menu.offers.removedDesc', { offerName: discount.offerName }),
        variant: 'default',
      });
    }
  };

  // Place an order
  const placeOrder = async (): Promise<Order | null> => {
    if (!user) {
      // User is not logged in, redirect to sign in page
      router.push('/signin');
      return null;
    }

    // Validate delivery availability if delivery is selected
    if (deliveryOption?.type === DeliveryType.DELIVERY) {
      const validation = validateDeliveryAvailability();
      if (!validation.isValid) {
        toast({
          title: 'Cannot place delivery order',
          description: validation.message,
          variant: 'destructive',
        });
        return null;
      }
    }

    try {
      // Create order items from cart items
      const orderItems = cartItems.map(item => ({
        id: item.id,
        name: item.title,
        price: item.price,
        quantity: item.quantity,
      }));
      
      // Calculate subtotal (without delivery fee and discounts)
      const originalSubtotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      const discountAmount = appliedDiscounts.reduce((sum, discount) => sum + discount.discountAmount, 0);
      const subtotal = originalSubtotal - discountAmount;

      // Calculate total with delivery fee if applicable
      const total = deliveryOption?.fee ? subtotal + deliveryOption.fee : subtotal;
      
      // Create the order with delivery and discount information
      const orderData: any = {
        userId: user.uid,
        items: orderItems,
        status: OrderStatus.ORDER_PLACED,
        subtotal,
        originalSubtotal,
        deliveryFee: deliveryOption?.fee || 0,
        total,
        paymentMethod: PaymentMethod.CASH,
        deliveryType: deliveryOption?.type === DeliveryType.DELIVERY ? DeliveryZoneType.DELIVERY :
                   deliveryOption?.type === DeliveryType.TABLE ? DeliveryZoneType.IN_HOUSE_TABLES :
                   DeliveryZoneType.PICK_UP,
        appliedDiscounts: appliedDiscounts.length > 0 ? appliedDiscounts : undefined,
        discountAmount: discountAmount > 0 ? discountAmount : undefined,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Only add optional fields if they have values
      if (deliveryOption?.type === DeliveryType.TABLE && deliveryOption.tableNumber) {
        orderData.tableNumber = deliveryOption.tableNumber;
      }

      if (deliveryOption?.type === DeliveryType.DELIVERY) {
        if (deliveryOption.deliveryAddress) {
          orderData.deliveryAddress = deliveryOption.deliveryAddress;
        }
        if (deliveryOption.deliveryZone?.id) {
          orderData.deliveryZoneId = deliveryOption.deliveryZone.id;
        }
      }

      const createdOrder = await createOrder(orderData);

      // Increment usage count for applied offers
      for (const discount of appliedDiscounts) {
        try {
          await incrementOfferUsage(discount.offerId);
        } catch (error) {
          console.error('Error incrementing offer usage:', error);
        }
      }

      // Award loyalty points for the order
      try {
        const loyaltyResponse = await fetch('/api/loyalty/process-order', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            orderId: createdOrder.id,
            userId: user.uid,
            orderAmount: total
          }),
        });

        if (loyaltyResponse.ok) {
          const loyaltyResult = await loyaltyResponse.json();
          console.log('Loyalty points awarded:', loyaltyResult.data);

          // Show additional success message about points earned
          if (loyaltyResult.data.pointsEarned > 0) {
            toast({
              title: t('loyalty.pointsEarned'),
              description: t('loyalty.pointsEarnedDescription', {
                points: loyaltyResult.data.pointsEarned,
                newBalance: loyaltyResult.data.newBalance
              }),
              variant: 'success',
            });
          }
        } else {
          console.error('Failed to process loyalty points:', await loyaltyResponse.text());
        }
      } catch (error) {
        console.error('Error processing loyalty points:', error);
        // Don't fail the order if loyalty points fail
      }

      // Clear the cart after successful order
      clearCart();

      // Show success message
      toast({
        title: 'Order placed successfully',
        description: 'Your order has been placed successfully. Please pay with cash upon delivery/pickup.',
        variant: 'success',
      });

      // Return the created order instead of redirecting
      return createdOrder;
    } catch (error) {
      console.error('Error placing order:', error);
      toast({
        title: 'Failed to place order',
        description: 'There was an error placing your order. Please try again.',
        variant: 'destructive',
      });
      return null;
    }
  };

  // Computed properties
  const isCartEmpty = cartItems.length === 0;
  const cartTotal = cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  const cartItemsCount = cartItems.reduce((count, item) => count + item.quantity, 0);
  const totalDiscount = appliedDiscounts.reduce((sum, discount) => sum + discount.discountAmount, 0);
  const finalTotal = Math.max(0, cartTotal - totalDiscount + (deliveryOption?.fee || 0));
  const totalWithDelivery = deliveryOption?.fee ? cartTotal + deliveryOption.fee : cartTotal;

  // Delivery availability validation
  const nonDeliveryItems = cartItems.filter(item => !item.isAvailableForDelivery);
  const hasNonDeliveryItems = nonDeliveryItems.length > 0;
  const canSelectDelivery = !hasNonDeliveryItems;

  // Validation function for delivery availability
  const validateDeliveryAvailability = () => {
    if (hasNonDeliveryItems) {
      const itemNames = nonDeliveryItems.map(item => item.title).join(', ');
      return {
        isValid: false,
        message: `The following items are not available for delivery: ${itemNames}. Please remove them or choose pickup/table service.`
      };
    }
    return { isValid: true };
  };

  // Fetch user profile when user changes
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (user) {
        try {
          const profile = await getUserProfile(user.uid);
          setUserProfile(profile);
        } catch (error) {
          console.error('Error fetching user profile:', error);
        }
      } else {
        setUserProfile(null);
      }
    };

    fetchUserProfile();
  }, [user]);

  // Refresh offers when user changes
  useEffect(() => {
    if (user) {
      refreshOffers();
    } else {
      setAvailableOffers([]);
      setAppliedDiscounts([]);
    }
  }, [user, refreshOffers]);

  // Context value
  const value = {
    cartItems,
    addToCart,
    updateQuantity,
    removeFromCart,
    clearCart,
    placeOrder,
    isCartEmpty,
    cartTotal,
    cartItemsCount,
    deliveryOption,
    setDeliveryOption,
    totalWithDelivery,
    // Delivery availability validation
    hasNonDeliveryItems,
    nonDeliveryItems,
    canSelectDelivery,
    validateDeliveryAvailability,
    // Discount functionality
    appliedDiscounts,
    availableOffers,
    applyOffer,
    removeDiscount,
    totalDiscount,
    finalTotal,
    refreshOffers,
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
} 