'use client';

import { 
  createContext, 
  useContext, 
  useState, 
  useEffect, 
  ReactNode 
} from 'react';
import { onAuthStateChanged, User, RecaptchaVerifier, ConfirmationResult } from 'firebase/auth';
import { auth } from '@/lib/firebase/config';
import {
  initializeRecaptcha,
  sendPhoneVerificationCode,
  verifyPhoneCode,
  logout as logoutUtil,
  setSessionCookie,
  formatInternationalPhoneNumber,
  validateInternationalPhoneNumber,
  updateUserDisplayName,
  cleanupRecaptcha,
  checkPhoneNumberExists
} from '@/lib/firebase/phone-auth-utils';
import { CountryCode, DEFAULT_COUNTRY } from '@/lib/country-codes';
import { toast } from '@/hooks/use-toast';

// Admin phone number - only this number will have admin access
const ADMIN_PHONE_NUMBER = '+16505555555';

/**
 * Check if a phone number belongs to an admin
 */
const isAdminPhoneNumber = (phoneNumber: string): boolean => {
  // Normalize phone numbers for comparison (remove spaces, dashes, etc.)
  const normalizePhone = (phone: string) => phone.replace(/[\s\-\(\)]/g, '');

  const normalizedInput = normalizePhone(phoneNumber);
  const normalizedAdmin = normalizePhone(ADMIN_PHONE_NUMBER);

  console.log('Checking admin phone:', { input: normalizedInput, admin: normalizedAdmin });
  return normalizedInput === normalizedAdmin;
};

// Phone authentication context type
interface PhoneAuthContextType {
  user: User | null;
  loading: boolean;
  isAdmin: boolean;
  adminCheckComplete: boolean;
  // Phone verification states
  verificationStep: 'phone' | 'code' | 'profile' | 'complete';
  phoneNumber: string;
  isCodeSent: boolean;
  isVerifying: boolean;
  // Methods
  sendVerificationCode: (phoneNumber: string, countryCode?: CountryCode) => Promise<void>;
  verifyCode: (code: string, displayName?: string) => Promise<void>;
  resendCode: () => Promise<void>;
  logout: () => Promise<void>;
  resetVerification: () => void;
}

// Create the auth context
const PhoneAuthContext = createContext<PhoneAuthContextType | undefined>(undefined);

// Custom hook to use the auth context
export const usePhoneAuth = () => {
  const context = useContext(PhoneAuthContext);
  if (context === undefined) {
    throw new Error('usePhoneAuth must be used within a PhoneAuthProvider');
  }
  return context;
};

interface PhoneAuthProviderProps {
  children: ReactNode;
}

/**
 * Phone Auth Provider Component
 * Provides phone-based authentication state and methods to the app
 */
export function PhoneAuthProvider({ children }: PhoneAuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const [adminCheckComplete, setAdminCheckComplete] = useState(false);

  // Phone verification states
  const [verificationStep, setVerificationStep] = useState<'phone' | 'code' | 'profile' | 'complete'>('phone');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isCodeSent, setIsCodeSent] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);

  // Loading timeout to prevent infinite loading
  useEffect(() => {
    const loadingTimeout = setTimeout(() => {
      if (loading) {
        console.warn('PhoneAuth: Loading timeout reached, setting loading to false');
        setLoading(false);
      }
    }, 10000); // 10 second timeout

    return () => clearTimeout(loadingTimeout);
  }, [loading]);
  
  // reCAPTCHA and confirmation
  const [recaptchaVerifier, setRecaptchaVerifier] = useState<RecaptchaVerifier | null>(null);
  const [confirmationResult, setConfirmationResult] = useState<ConfirmationResult | null>(null);

  // Listen for auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      console.log("PhoneAuthContext: Auth state changed - user:", user ? user.uid : "no user");
      setUser(user);
      setLoading(false);

      if (user) {
        // Handle async operations without blocking the auth state change
        const handleUserSetup = async () => {
          try {
            // Set session cookie with timeout
            const sessionPromise = setSessionCookie(user);
            const sessionTimeoutPromise = new Promise<void>((_, reject) =>
              setTimeout(() => reject(new Error('Session cookie timeout')), 3000)
            );

            await Promise.race([sessionPromise, sessionTimeoutPromise]);
          } catch (error) {
            console.error('Error setting session cookie:', error);
            // Don't fail the auth process if session cookie fails
          }

          // Check admin status based on phone number (instant check)
          const userPhoneNumber = user.phoneNumber;
          console.log('PhoneAuthContext: Checking admin status for phone:', userPhoneNumber);

          if (userPhoneNumber) {
            const adminStatus = isAdminPhoneNumber(userPhoneNumber);
            console.log('PhoneAuthContext: Admin check result:', adminStatus);
            setIsAdmin(adminStatus);
          } else {
            console.log('PhoneAuthContext: No phone number found, setting admin to false');
            setIsAdmin(false);
          }

          setAdminCheckComplete(true);
          console.log('PhoneAuthContext: Admin check completed');
        };

        // Run async operations without awaiting to prevent blocking
        handleUserSetup().catch(error => {
          console.error('Error in user setup:', error);
          setIsAdmin(false);
        }).finally(() => {
          // Ensure loading is set to false even if setup fails
          console.log('PhoneAuthContext: User setup completed, setting loading to false');
          setLoading(false);
        });

        // Only set verification as complete if we're not in an active verification flow
        if (verificationStep === 'phone') {
          setVerificationStep('complete');
        }
      } else {
        setIsAdmin(false);
        setAdminCheckComplete(true);
        setVerificationStep('phone');
        resetVerification();
        // Set loading to false for non-authenticated users
        setLoading(false);
      }
    });

    return () => {
      unsubscribe();
      // Cleanup reCAPTCHA on unmount
      try {
        cleanupRecaptcha();
      } catch (error) {
        console.warn('Error cleaning up reCAPTCHA on unmount:', error);
      }
    };
  }, []);

  /**
   * Send verification code to phone number
   */
  const sendVerificationCode = async (phoneNumber: string, countryCode?: CountryCode) => {
    try {
      setIsVerifying(true);

      // Use provided country or default to Saudi Arabia
      const country = countryCode || DEFAULT_COUNTRY;

      // Validate phone number for the selected country
      if (!validateInternationalPhoneNumber(phoneNumber, country)) {
        throw new Error(`Please enter a valid ${country.name} phone number`);
      }

      const formattedPhone = formatInternationalPhoneNumber(phoneNumber, country);
      setPhoneNumber(formattedPhone);

      // Check if phone number already exists
      const phoneExists = await checkPhoneNumberExists(formattedPhone);
      if (phoneExists) {
        // For existing users, proceed with login
        console.log('Phone number exists, proceeding with login');
      } else {
        // For new users, we'll create profile after verification
        console.log('New phone number, will create profile after verification');
      }

      // Initialize reCAPTCHA if not already done
      if (!recaptchaVerifier) {
        try {
          const verifier = initializeRecaptcha('recaptcha-container');
          setRecaptchaVerifier(verifier);

          // Send verification code
          const confirmation = await sendPhoneVerificationCode(formattedPhone, verifier, country);
          setConfirmationResult(confirmation);
        } catch (recaptchaError) {
          console.error('reCAPTCHA initialization failed:', recaptchaError);
          throw new Error('Failed to initialize verification system. Please refresh the page and try again.');
        }
      } else {
        try {
          // Use existing verifier
          const confirmation = await sendPhoneVerificationCode(formattedPhone, recaptchaVerifier, country);
          setConfirmationResult(confirmation);
        } catch (verificationError) {
          console.error('Verification code sending failed:', verificationError);
          // Try to reinitialize reCAPTCHA on failure
          cleanupRecaptcha();
          setRecaptchaVerifier(null);
          throw new Error('Failed to send verification code. Please try again.');
        }
      }

      setIsCodeSent(true);
      setVerificationStep('code');
      
      toast({
        title: "Verification code sent",
        description: `We've sent a verification code to ${formattedPhone}`,
      });

    } catch (error: any) {
      console.error('Error sending verification code:', error);
      
      let errorMessage = 'Failed to send verification code. Please try again.';
      
      if (error.code === 'auth/too-many-requests') {
        errorMessage = 'Too many requests. Please try again later.';
      } else if (error.code === 'auth/invalid-phone-number') {
        errorMessage = 'Invalid phone number format.';
      }
      
      toast({
        variant: "destructive",
        title: "Error",
        description: errorMessage,
      });
      
      throw error;
    } finally {
      setIsVerifying(false);
    }
  };

  /**
   * Verify SMS code and complete authentication
   */
  const verifyCode = async (code: string, displayName?: string) => {
    try {
      setIsVerifying(true);

      if (!confirmationResult) {
        throw new Error('No verification in progress. Please request a new code.');
      }

      // Verify the code
      const userCredential = await verifyPhoneCode(code, confirmationResult);
      const firebaseUser = userCredential.user;

      // Update user state immediately to avoid race condition
      setUser(firebaseUser);

      // Check if this is a new user (no display name set)
      const isNewUser = !firebaseUser.displayName;

      if (isNewUser && displayName) {
        // Update display name for new users
        await updateUserDisplayName(firebaseUser, displayName);
      }

      // Create or update user profile in Firestore
      let userProfile = null;
      try {
        const { getUserProfile, createUserProfile, updateUserProfile } = await import('@/lib/firebase/firestore');

        const existingProfile = await getUserProfile(firebaseUser.uid);

        if (!existingProfile) {
          // Create new user profile
          userProfile = await createUserProfile(firebaseUser.uid, {
            displayName: displayName || firebaseUser.displayName || '',
            email: '', // No email for phone auth
            phoneNumber: phoneNumber,
            photoURL: firebaseUser.photoURL || '',
          });
        } else {
          // Update existing profile with phone number if needed
          if (existingProfile.phoneNumber !== phoneNumber) {
            await updateUserProfile(firebaseUser.uid, {
              phoneNumber: phoneNumber,
            });
          }
          userProfile = existingProfile;
        }

        // Check admin status based on phone number (instant check)
        const userPhoneNumber = firebaseUser.phoneNumber;
        console.log('PhoneAuth: Checking admin status for phone during verification:', userPhoneNumber);

        if (userPhoneNumber) {
          const adminStatus = isAdminPhoneNumber(userPhoneNumber);
          console.log('PhoneAuth: Admin check result during verification:', adminStatus);
          setIsAdmin(adminStatus);
        } else {
          console.log('PhoneAuth: No phone number found during verification, setting admin to false');
          setIsAdmin(false);
        }

        setAdminCheckComplete(true);
        console.log('PhoneAuth: Admin check completed during verification');
      } catch (profileError) {
        console.error('Error creating/updating user profile:', profileError);
        // Don't fail authentication if profile creation fails
        setIsAdmin(false);
      }

      // Set session cookie (with error handling and timeout)
      try {
        const sessionPromise = setSessionCookie(firebaseUser);
        const sessionTimeoutPromise = new Promise<void>((_, reject) =>
          setTimeout(() => reject(new Error('Session cookie timeout')), 3000)
        );

        await Promise.race([sessionPromise, sessionTimeoutPromise]);
      } catch (sessionError) {
        console.error('Error setting session cookie:', sessionError);
        // Don't fail authentication if session cookie fails
      }

      console.log('PhoneAuth: Setting verification step to complete');
      console.log('PhoneAuth: User:', firebaseUser.uid);
      console.log('PhoneAuth: IsAdmin:', isAdmin);

      // Set verification step to complete immediately to trigger redirect
      setVerificationStep('complete');

      toast({
        title: "Phone verified successfully",
        description: "Welcome to BarcodeCafe!",
      });

    } catch (error: any) {
      console.error('Error verifying code:', error);
      
      let errorMessage = 'Invalid verification code. Please try again.';
      
      if (error.code === 'auth/invalid-verification-code') {
        errorMessage = 'Invalid verification code. Please check and try again.';
      } else if (error.code === 'auth/code-expired') {
        errorMessage = 'Verification code has expired. Please request a new one.';
      }
      
      toast({
        variant: "destructive",
        title: "Verification failed",
        description: errorMessage,
      });
      
      throw error;
    } finally {
      setIsVerifying(false);
    }
  };

  /**
   * Resend verification code
   */
  const resendCode = async () => {
    if (!phoneNumber) {
      throw new Error('No phone number to resend code to');
    }
    
    // Reset states
    setIsCodeSent(false);
    setConfirmationResult(null);
    
    // Clean up existing reCAPTCHA
    cleanupRecaptcha();
    setRecaptchaVerifier(null);
    
    // Send new code
    await sendVerificationCode(phoneNumber);
  };

  /**
   * Logout user
   */
  const logout = async () => {
    try {
      await logoutUtil();
      resetVerification();
      
      toast({
        title: "Logged out successfully",
        description: "You have been logged out of your account.",
      });
    } catch (error) {
      console.error('Logout failed:', error);
      throw error;
    }
  };

  /**
   * Reset verification state
   */
  const resetVerification = () => {
    setVerificationStep('phone');
    setPhoneNumber('');
    setIsCodeSent(false);
    setIsVerifying(false);
    setConfirmationResult(null);
    cleanupRecaptcha();
    setRecaptchaVerifier(null);
  };

  // Context value
  const value = {
    user,
    loading,
    isAdmin,
    adminCheckComplete,
    verificationStep,
    phoneNumber,
    isCodeSent,
    isVerifying,
    sendVerificationCode,
    verifyCode,
    resendCode,
    logout,
    resetVerification,
  };

  return (
    <PhoneAuthContext.Provider value={value}>
      {children}
    </PhoneAuthContext.Provider>
  );
}
