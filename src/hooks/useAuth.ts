'use client';

import { usePhoneAuth } from '@/contexts/PhoneAuthContext';

/**
 * Compatibility hook that maps the new PhoneAuthContext to the old AuthContext interface
 * This allows existing components to continue working without major changes
 */
export const useAuth = () => {
  const phoneAuth = usePhoneAuth();

  // Map the new interface to the old one
  return {
    user: phoneAuth.user,
    loading: phoneAuth.loading,
    isEmailVerified: true, // Phone auth users are always "verified"
    isAdmin: phoneAuth.isAdmin,
    adminCheckComplete: phoneAuth.adminCheckComplete,
    
    // Legacy methods - these will throw errors since we don't support email auth anymore
    login: async (email: string, password: string) => {
      throw new Error('Email/password login is no longer supported. Please use phone authentication.');
    },
    
    loginWithGoogle: async () => {
      throw new Error('Google login is no longer supported. Please use phone authentication.');
    },
    
    register: async (email: string, password: string) => {
      throw new Error('Email/password registration is no longer supported. Please use phone authentication.');
    },
    
    sendVerification: async () => {
      throw new Error('Email verification is no longer needed. Phone authentication is used instead.');
    },
    
    resetPassword: async (email: string) => {
      throw new Error('Password reset is no longer supported. Please use phone authentication.');
    },
    
    logout: phoneAuth.logout,
  };
};
