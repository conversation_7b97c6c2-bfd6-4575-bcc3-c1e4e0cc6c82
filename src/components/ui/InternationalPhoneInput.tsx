'use client';

import { useState, useEffect, useRef } from 'react';
import { ChevronDown, Search } from 'lucide-react';
import { CountryCode, COUNTRY_CODES, DEFAULT_COUNTRY, getPhoneNumberPlaceholder } from '@/lib/country-codes';
import { useLocale } from '@/contexts/LocaleContext';

interface InternationalPhoneInputProps {
  value: string;
  onChange: (value: string, country: CountryCode) => void;
  onCountryChange?: (country: CountryCode) => void;
  selectedCountry?: CountryCode;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  error?: boolean;
}

export default function InternationalPhoneInput({
  value,
  onChange,
  onCountryChange,
  selectedCountry = DEFAULT_COUNTRY,
  placeholder,
  disabled = false,
  className = '',
  error = false
}: InternationalPhoneInputProps) {
  const { t, dir, isClient } = useLocale();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentCountry, setCurrentCountry] = useState<CountryCode>(selectedCountry);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Filter countries based on search term
  const filteredCountries = COUNTRY_CODES.filter(country => {
    const searchLower = searchTerm.toLowerCase();
    return (
      country.name.toLowerCase().includes(searchLower) ||
      country.nameAr.includes(searchTerm) ||
      country.dialCode.includes(searchTerm) ||
      country.code.toLowerCase().includes(searchLower)
    );
  });

  const handleCountrySelect = (country: CountryCode) => {
    setCurrentCountry(country);
    setIsDropdownOpen(false);
    setSearchTerm('');
    onCountryChange?.(country);
    
    // Update the phone number with new country code if needed
    const cleanedValue = value.replace(/\D/g, '');
    onChange(cleanedValue, country);
    
    // Focus back to input
    inputRef.current?.focus();
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    onChange(inputValue, currentCountry);
  };

  const displayPlaceholder = placeholder || getPhoneNumberPlaceholder(currentCountry);
  const rtlClass = dir === 'rtl' ? 'text-right' : 'text-left';

  return (
    <div className={`relative ${className}`}>
      <div className={`flex rounded-lg border ${error ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'} ${disabled ? 'opacity-50' : ''}`}>
        {/* Country Code Selector */}
        <div className="relative" ref={dropdownRef}>
          <button
            type="button"
            onClick={() => !disabled && setIsDropdownOpen(!isDropdownOpen)}
            disabled={disabled}
            className={`flex items-center gap-2 px-3 py-3 bg-gray-50 dark:bg-[#242832] border-r border-gray-300 dark:border-gray-600 rounded-l-lg hover:bg-gray-100 dark:hover:bg-[#2a303a] transition-colors ${disabled ? 'cursor-not-allowed' : 'cursor-pointer'}`}
          >
            <span className="text-lg">{currentCountry.flag}</span>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {currentCountry.dialCode}
            </span>
            <ChevronDown className={`h-4 w-4 text-gray-500 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />
          </button>

          {/* Dropdown */}
          {isDropdownOpen && (
            <div className="absolute top-full left-0 mt-1 w-80 bg-white dark:bg-[#1d2127] border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 max-h-80 overflow-hidden">
              {/* Search */}
              <div className="p-3 border-b border-gray-200 dark:border-gray-700">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder={isClient ? t('phoneAuth.searchCountry') : "Search countries..."}
                    className={`w-full pl-10 pr-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-[#242832] text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-[#66B8B1] focus:border-transparent ${rtlClass}`}
                  />
                </div>
              </div>

              {/* Countries List */}
              <div className="max-h-60 overflow-y-auto">
                {filteredCountries.length > 0 ? (
                  filteredCountries.map((country) => (
                    <button
                      key={country.code}
                      type="button"
                      onClick={() => handleCountrySelect(country)}
                      className={`w-full flex items-center gap-3 px-3 py-2 text-left hover:bg-gray-50 dark:hover:bg-[#242832] transition-colors ${
                        currentCountry.code === country.code ? 'bg-[#83EAED]/10 dark:bg-[#5DBDC0]/10' : ''
                      }`}
                    >
                      <span className="text-lg">{country.flag}</span>
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                          {isClient && dir === 'rtl' ? country.nameAr : country.name}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {country.dialCode}
                        </div>
                      </div>
                    </button>
                  ))
                ) : (
                  <div className="px-3 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                    {isClient ? t('phoneAuth.noCountriesFound') : "No countries found"}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Phone Number Input */}
        <input
          ref={inputRef}
          type="tel"
          value={value}
          onChange={handlePhoneChange}
          placeholder={displayPlaceholder}
          disabled={disabled}
          className={`flex-1 px-4 py-3 bg-white dark:bg-[#242832] text-gray-900 dark:text-gray-100 rounded-r-lg focus:ring-2 focus:ring-[#66B8B1] dark:focus:ring-[#5DBDC0] focus:border-transparent outline-none ${rtlClass} ${disabled ? 'cursor-not-allowed' : ''}`}
        />
      </div>

      {/* Help Text */}
      <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
        {isClient ? t('phoneAuth.internationalHelp') : "Enter your phone number with country code"}
      </div>
    </div>
  );
}
