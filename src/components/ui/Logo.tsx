"use client";

import Link from "next/link";
import Image from "next/image";

interface LogoProps {
  withText?: boolean;
  textSize?: "sm" | "md" | "lg";
  iconSize?: "sm" | "md" | "lg";
  linkTo?: string;
}

export default function Logo({
  withText = true,
  textSize = "md",
  iconSize = "md",
  linkTo = "/"
}: LogoProps) {
  // Map text sizes to Tailwind classes
  const textSizeClasses = {
    sm: "text-lg",
    md: "text-xl",
    lg: "text-2xl"
  };

  // Map icon sizes to pixel dimensions for the SVG logo
  const iconSizeClasses = {
    sm: { width: 64, height: 64 },
    md: { width: 80, height: 80 },
    lg: { width: 96, height: 96 }
  };

  const logoContent = (
    <div className="flex items-center gap-2 logo-container icon-exempt">
      <Image
        src="/logo-default.svg"
        alt="BarcodeCafe Logo"
        width={iconSizeClasses[iconSize].width}
        height={iconSizeClasses[iconSize].height}
        className="icon-exempt"
        priority
      />
    </div>
  );
  
  // If linkTo is provided, wrap the logo in a Link
  if (linkTo) {
    return <Link href={linkTo}>{logoContent}</Link>;
  }
  
  // Otherwise, just return the logo
  return logoContent;
} 