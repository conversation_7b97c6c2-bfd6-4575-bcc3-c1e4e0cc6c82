"use client";

import { useState, useEffect } from "react";
import { useLocale } from "@/contexts/LocaleContext";
import { Offer, MenuItem, PackageItem } from "@/types/models";
import RiyalSymbol from "@/components/ui/RiyalSymbol";
// import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
// import { Checkbox } from "@/components/ui/checkbox";

interface PackageDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  offer: Offer | null;
  packageItems: MenuItem[];
  allMenuItems: MenuItem[];
  onAddToCart: (offer: Offer, selectedItems: MenuItem[], customizations: PackageCustomization[]) => void;
}

export interface PackageCustomization {
  originalItemId: string;
  selectedItemId: string;
  quantity: number;
  isIncluded: boolean;
}

export default function PackageDetailModal({
  isOpen,
  onClose,
  offer,
  packageItems,
  allMenuItems,
  onAddToCart
}: PackageDetailModalProps) {
  const { t, isClient, locale } = useLocale();
  const isRTL = locale === 'ar';

  const [customizations, setCustomizations] = useState<PackageCustomization[]>([]);
  const [quantity, setQuantity] = useState(1);



  // Initialize customizations when offer changes
  useEffect(() => {
    if (offer && packageItems.length > 0) {
      // Create package items from the passed packageItems with default quantity of 1
      const initialCustomizations: PackageCustomization[] = packageItems.map(item => ({
        originalItemId: item.id,
        selectedItemId: item.id,
        quantity: 1, // Default quantity
        isIncluded: true // All items included by default
      }));
      setCustomizations(initialCustomizations);
    }
  }, [offer, packageItems]);

  // Check if modal should be open first
  if (!isOpen) return null;

  // Then check if we have the required data
  if (!offer || packageItems.length === 0) {
    // Show a loading or error state instead of returning null
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        {/* Backdrop */}
        <div
          className="fixed inset-0 bg-black/50"
          onClick={onClose}
        ></div>

        {/* Modal Content */}
        <div className="relative z-50 w-full max-w-md bg-white dark:bg-[#392e23] rounded-lg shadow-xl m-4 p-6">
          <div className="text-center">
            <h2 className="text-xl font-bold text-[#703f23] dark:text-[#e09a62] mb-4">
              {isClient ? t('packages.loadingPackage') : 'Loading Package...'}
            </h2>
            <p className="text-[#94795e] dark:text-[#b49678] mb-4">
              {isClient ? t('packages.loadingItems') : 'Loading package items...'}
            </p>
            <button
              onClick={onClose}
              className="px-4 py-2 bg-[#c27845] dark:bg-[#d27d46] text-white rounded hover:bg-[#b06735] dark:hover:bg-[#c16e37]"
            >
              {isClient ? t('common.close') : 'Close'}
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Calculate pricing
  const originalPrice = packageItems.reduce((total, item) => {
    return total + item.price; // Default quantity of 1 for each item
  }, 0);

  // Use packagePrice if available, otherwise use discountValue as the package price
  const packagePrice = offer.conditions.packagePrice ||
    (offer.discountType === 'fixed_amount' ? offer.discountValue : originalPrice * (1 - offer.discountValue / 100));
  const savings = originalPrice - packagePrice;
  const totalPrice = packagePrice * quantity;

  const handleCustomizationChange = (originalItemId: string, field: keyof PackageCustomization, value: any) => {
    setCustomizations(prev => prev.map(custom =>
      custom.originalItemId === originalItemId
        ? { ...custom, [field]: value }
        : custom
    ));
  };

  const handleAddToCart = () => {
    if (offer) {
      onAddToCart(offer, packageItems, customizations);
      onClose();
    }
  };

  const getItemById = (itemId: string): MenuItem | undefined => {
    return allMenuItems.find(item => item.id === itemId);
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50"
        onClick={onClose}
      ></div>

      {/* Modal Content */}
      <div className="relative z-50 w-full max-w-2xl max-h-[90vh] overflow-y-auto bg-white dark:bg-[#392e23] rounded-lg shadow-xl m-4" dir={isRTL ? "rtl" : "ltr"}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-bold text-[#703f23] dark:text-[#e09a62] flex items-center gap-2">
            <i className="fa-solid fa-box-open text-green-600"></i>
            {locale === 'ar' && offer.name_ar ? offer.name_ar : offer.name}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <i className="fa-solid fa-times text-xl"></i>
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="space-y-6">
            {/* Package Description */}
            <div>
              <p className="text-[#94795e] dark:text-[#b49678]">
                {locale === 'ar' && offer.description_ar ? offer.description_ar : offer.description}
              </p>
            </div>

            {/* Pricing Summary */}
            <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium text-[#703f23] dark:text-[#e09a62]">
                {isClient ? t('packages.packagePrice') : 'Package Price'}
              </span>
              <span className="text-2xl font-bold text-green-600 dark:text-green-400">
                <RiyalSymbol size={18} className="text-green-600 dark:text-green-400" /> {packagePrice.toFixed(2)}
              </span>
            </div>
            {savings > 0 && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">
                  {isClient ? t('packages.individualPrice') : 'Individual Price'}
                </span>
                <span className="line-through text-gray-500 dark:text-gray-400">
                  <RiyalSymbol size={16} className="text-gray-500 dark:text-gray-400" /> {originalPrice.toFixed(2)}
                </span>
              </div>
            )}
            {savings > 0 && (
              <div className="flex items-center justify-between text-sm font-medium text-green-600 dark:text-green-400">
                <span>{isClient ? t('packages.youSave') : 'You Save'}</span>
                <span><RiyalSymbol size={14} className="text-green-600 dark:text-green-400" /> {savings.toFixed(2)}</span>
              </div>
            )}
            </div>

            {/* Package Items */}
            <div>
            <h3 className="font-medium text-[#703f23] dark:text-[#e09a62] mb-3">
              {isClient ? t('packages.includedItems') : 'Included Items'}
            </h3>
            <div className="space-y-3">
              {packageItems.map((item, index) => {
                const customization = customizations.find(c => c.originalItemId === item.id);

                if (!customization) return null;

                return (
                  <div key={item.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                    <div className="flex items-start gap-3">
                      {/* Item Image */}
                      <div className="w-16 h-16 rounded-lg overflow-hidden flex-shrink-0 bg-gray-100 dark:bg-gray-800">
                        {item.image ? (
                          <img src={item.image} alt={item.title} className="w-full h-full object-cover" />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <i className="fa-solid fa-utensils text-gray-400"></i>
                          </div>
                        )}
                      </div>

                      <div className="flex-1">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-medium text-[#703f23] dark:text-[#e09a62]">
                              {locale === 'ar' && item.title_ar ? item.title_ar : item.title}
                            </h4>
                            <p className="text-sm text-[#94795e] dark:text-[#b49678] mt-1">
                              {isClient ? t('packages.quantity') : 'Quantity'}: {customization.quantity}
                            </p>
                            <p className="text-sm text-green-600 dark:text-green-400 font-medium">
                              <RiyalSymbol size={12} className="text-gray-600 dark:text-gray-400" /> {item.price.toFixed(2)}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
            </div>

            {/* Quantity Selector */}
            <div className="flex items-center justify-between">
            <span className="font-medium text-[#703f23] dark:text-[#e09a62]">
              {isClient ? t('packages.packageQuantity') : 'Package Quantity'}
            </span>
            <div className="flex items-center gap-3">
              <button
                onClick={() => setQuantity(Math.max(1, quantity - 1))}
                className="w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-[#703f23] dark:text-[#e09a62] hover:bg-gray-200 dark:hover:bg-gray-600"
              >
                <i className="fa-solid fa-minus text-xs"></i>
              </button>
              <span className="font-medium text-[#703f23] dark:text-[#e09a62] min-w-[2rem] text-center">
                {quantity}
              </span>
              <button
                onClick={() => setQuantity(quantity + 1)}
                className="w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-[#703f23] dark:text-[#e09a62] hover:bg-gray-200 dark:hover:bg-gray-600"
              >
                <i className="fa-solid fa-plus text-xs"></i>
              </button>
            </div>
            </div>

            {/* Total Price */}
            <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
            <div className="flex items-center justify-between text-lg font-bold">
              <span className="text-[#703f23] dark:text-[#e09a62]">
                {isClient ? t('packages.totalPrice') : 'Total Price'}
              </span>
              <span className="text-green-600 dark:text-green-400">
                <RiyalSymbol size={16} className="text-green-600 dark:text-green-400" /> {totalPrice.toFixed(2)}
              </span>
            </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={onClose}
              className="flex-1 border-gray-300 dark:border-gray-600"
            >
              {isClient ? t('common.cancel') : 'Cancel'}
            </Button>
            <Button
              onClick={handleAddToCart}
              className="flex-1 bg-[#c27845] dark:bg-[#d27d46] text-white hover:bg-[#b06735] dark:hover:bg-[#c16e37]"
            >
              <i className="fa-solid fa-cart-plus mr-2"></i>
              {isClient ? t('packages.addToCart') : 'Add to Cart'}
            </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
