"use client";

import { useRouter } from "next/navigation";
import DarkModeToggle from "@/components/ui/DarkModeToggle";
import LanguageSwitcher from "@/components/ui/LanguageSwitcher";
import Logo from "@/components/ui/Logo";
import UserAvatar from "@/components/ui/UserAvatar";
import { useAuth } from "@/hooks/useAuth";
import { useLocale } from "@/contexts/LocaleContext";

interface HeaderProps {
  toggleSidebar: () => void;
}

export default function Header({ toggleSidebar }: HeaderProps) {
  const { user, logout } = useAuth();
  const { t, isClient } = useLocale();
  const router = useRouter();
  
  // Use user's avatar from Firebase if available
  const userAvatarSrc = user?.photoURL || undefined;
  const displayName = user?.displayName || 'User';
  
  const handleLogout = async () => {
    try {
      await logout();
      // After logout, redirect to signin page
      router.push('/signin');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };
  
  return (
    <header className="bg-white dark:bg-[#1d2127] shadow-sm py-4">
      <div className="container mx-auto px-4 flex justify-between items-center">
        <div className="flex items-center">
          <button 
            className="md:hidden px-2 py-1 mr-3 text-gray-600 dark:text-gray-300"
            onClick={toggleSidebar}
            aria-label="Toggle menu"
          >
            <i className="fa-solid fa-bars text-xl"></i>
          </button>
          <Logo linkTo="/" />
        </div>
        
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-3">
            <DarkModeToggle />
            <LanguageSwitcher />
          </div>
          
          <i className="fa-regular fa-bell text-gray-600 dark:text-gray-300 text-xl cursor-pointer"></i>
          
          <div className="relative group">
            <div className="flex items-center gap-2 cursor-pointer">
              <UserAvatar 
                src={userAvatarSrc}
                alt={displayName} 
                size="md"
              />
              <span className="font-medium dark:text-white hidden sm:inline-block">{displayName}</span>
            </div>
            
            {/* Dropdown menu */}
            <div className="absolute right-0 top-full mt-2 w-48 bg-white dark:bg-[#1d2127] rounded-md shadow-lg z-10 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300">
              <button 
                onClick={handleLogout}
                className="flex items-center gap-2 w-full px-4 py-3 text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-[#242832] rounded-md transition-colors"
              >
                <i className="fa-solid fa-sign-out-alt"></i>
                <span>{isClient ? t('common.logout') : 'Logout'}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
} 