"use client";

import { useState, useEffect } from "react";
import { useLocale } from "@/contexts/LocaleContext";
import { Order, OrderStatus, PaymentMethod } from "@/types/models";
import { formatDate } from "@/lib/utils";
import { updateOrder } from "@/lib/firebase/firestore";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import ReceiptModal from "@/components/receipt/ReceiptModal";
import CancelOrderModal from "@/components/orders/CancelOrderModal";
import RiyalSymbol from "@/components/ui/RiyalSymbol";

interface OrderDetailsModalProps {
  order: Order | null;
  isOpen: boolean;
  onClose: () => void;
  onOrderUpdated: (updatedOrder: Order) => void;
}

export default function OrderDetailsModal({
  order,
  isOpen,
  onClose,
  onOrderUpdated
}: OrderDetailsModalProps) {
  const { t, isClient, locale } = useLocale();
  const [isUpdating, setIsUpdating] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<OrderStatus>(order?.status || OrderStatus.ORDER_PLACED);
  const [isReceiptModalOpen, setIsReceiptModalOpen] = useState(false);
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);

  // Open receipt modal
  const handlePrintReceipt = () => {
    setIsReceiptModalOpen(true);
  };

  // Open cancel modal
  const handleCancelOrder = () => {
    setIsCancelModalOpen(true);
  };

  // Handle order cancellation
  const handleOrderCancelled = (cancelledOrder: Order) => {
    onOrderUpdated(cancelledOrder);
    onClose();
  };

  // Update selected status when order changes
  useEffect(() => {
    if (order?.status) {
      setSelectedStatus(order.status);
    }
  }, [order?.status]);

  if (!order) return null;

  const getOrderStatusLabel = (status: OrderStatus): string => {
    switch (status) {
      case OrderStatus.ORDER_PLACED:
        return isClient ? t("orders.status.orderPlaced") : "Order Placed";
      case OrderStatus.PREPARING:
        return isClient ? t("orders.status.preparing") : "Preparing";
      case OrderStatus.READY_FOR_PICKUP:
        return isClient ? t("orders.status.readyForPickup") : "Ready for Pickup";
      case OrderStatus.OUT_FOR_DELIVERY:
        return isClient ? t("orders.status.outForDelivery") : "Out for Delivery";
      case OrderStatus.DELIVERED:
        return isClient ? t("orders.status.delivered") : "Delivered";
      case OrderStatus.CANCELLED:
        return isClient ? t("orders.status.cancelled") : "Cancelled";
      default:
        return status;
    }
  };

  const getStatusBadgeClass = (status: OrderStatus): string => {
    switch (status) {
      case OrderStatus.ORDER_PLACED:
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-500";
      case OrderStatus.PREPARING:
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-500";
      case OrderStatus.READY_FOR_PICKUP:
        return "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-500";
      case OrderStatus.OUT_FOR_DELIVERY:
        return "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-500";
      case OrderStatus.DELIVERED:
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-500";
      case OrderStatus.CANCELLED:
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-500";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-500";
    }
  };

  const getPaymentMethodLabel = (method: PaymentMethod): string => {
    switch (method) {
      case PaymentMethod.CREDIT_CARD:
        return isClient ? t("orders.paymentMethods.creditCard") : "Credit Card";
      case PaymentMethod.DEBIT_CARD:
        return isClient ? t("orders.paymentMethods.debitCard") : "Debit Card";
      case PaymentMethod.CASH:
        return isClient ? t("orders.paymentMethods.cash") : "Cash";
      case PaymentMethod.GIFT_CARD:
        return isClient ? t("orders.paymentMethods.giftCard") : "Gift Card";
      case PaymentMethod.LOYALTY_POINTS:
        return isClient ? t("orders.paymentMethods.loyaltyPoints") : "Loyalty Points";
      default:
        return method;
    }
  };

  const handleStatusUpdate = async () => {
    if (selectedStatus === order.status) return;

    setIsUpdating(true);
    try {
      const updatedOrder = await updateOrder(order.id, {
        status: selectedStatus,
        updatedAt: new Date()
      });
      
      onOrderUpdated(updatedOrder);
      onClose();
    } catch (error) {
      console.error("Error updating order status:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">
            {isClient ? t("orders.orderDetails") : "Order Details"}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Order Header */}
          <div className="flex flex-col md:flex-row md:justify-between md:items-start gap-4">
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {isClient ? t("orders.orderNumber") : "Order Number"}: #{order.id.slice(-8).toUpperCase()}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {order.createdAt instanceof Date 
                  ? formatDate(order.createdAt, true) 
                  : typeof order.createdAt === 'string' 
                    ? formatDate(new Date(order.createdAt), true)
                    : ''}
              </p>
            </div>
            <div className="flex items-center gap-3">
              <span className={`inline-flex px-3 py-1 text-sm font-medium rounded-full ${getStatusBadgeClass(order.status)}`}>
                {getOrderStatusLabel(order.status)}
              </span>
            </div>
          </div>

          {/* Order Items */}
          <div className="bg-gray-50 dark:bg-[#242832] rounded-lg p-4">
            <h3 className="text-lg font-medium mb-4 dark:text-gray-100">
              {isClient ? t("orders.items") : "Order Items"}
            </h3>
            <div className="space-y-3">
              {(order.items || []).map((item, index) => (
                <div key={index} className="flex justify-between items-start">
                  <div className="flex-1">
                    <p className="font-medium dark:text-gray-100">
                      {item.quantity || 0}x {item.name || 'Unknown Item'}
                    </p>
                    {item.options && item.options.length > 0 && (
                      <ul className="mt-1 space-y-1">
                        {item.options.map((option, optIndex) => (
                          <li key={optIndex} className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                            <span className="w-4 h-0 border-t border-gray-300 dark:border-gray-600 mr-2"></span>
                            {option.name || 'Option'}: {option.value || 'N/A'}
                            {(option.priceAdjustment || 0) !== 0 && (
                              <span className="ml-1 flex items-center gap-1">
                                ({(option.priceAdjustment || 0) > 0 ? '+' : ''}
                                <RiyalSymbol size={10} className="text-gray-500 dark:text-gray-400" />
                                {(option.priceAdjustment || 0).toFixed(2)})
                              </span>
                            )}
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                  <div className="text-right">
                    <p className="font-medium dark:text-gray-100 flex items-center gap-1 justify-end">
                      <RiyalSymbol size={12} className="text-gray-800 dark:text-gray-100" />
                      {((item.price || 0) * (item.quantity || 0)).toFixed(2)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Delivery Information */}
          {(order.deliveryAddress || order.tableNumber) && (
            <div className="bg-gray-50 dark:bg-[#242832] rounded-lg p-4">
              <h3 className="text-lg font-medium mb-3 dark:text-gray-100">
                {isClient ? t("admin.deliveryInfo") : "Delivery Information"}
              </h3>
              {order.deliveryAddress && (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  <span className="font-medium">{isClient ? t("checkout.deliveryAddress") : "Address"}:</span> {order.deliveryAddress}
                </p>
              )}
              {order.tableNumber && (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  <span className="font-medium">{isClient ? t("checkout.tableNumber") : "Table"}:</span> {order.tableNumber}
                </p>
              )}
            </div>
          )}

          {/* Special Instructions */}
          {order.specialInstructions && (
            <div className="bg-gray-50 dark:bg-[#242832] rounded-lg p-4">
              <h3 className="text-lg font-medium mb-3 dark:text-gray-100">
                {isClient ? t("orders.specialInstructions") : "Special Instructions"}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {order.specialInstructions}
              </p>
            </div>
          )}

          {/* Order Summary */}
          <div className="bg-gray-50 dark:bg-[#242832] rounded-lg p-4">
            <h3 className="text-lg font-medium mb-3 dark:text-gray-100">
              {isClient ? t("orders.summary") : "Order Summary"}
            </h3>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600 dark:text-gray-400">{isClient ? t("orders.subtotal") : "Subtotal"}</span>
                <span className="dark:text-gray-100 flex items-center gap-1">
                  <RiyalSymbol size={12} className="text-gray-800 dark:text-gray-100" />
                  {(order.subtotal || 0).toFixed(2)}
                </span>
              </div>
              {(order.deliveryFee || 0) > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">{isClient ? t("checkout.deliveryFee") : "Delivery Fee"}</span>
                  <span className="dark:text-gray-100 flex items-center gap-1">
                    <RiyalSymbol size={12} className="text-gray-800 dark:text-gray-100" />
                    {(order.deliveryFee || 0).toFixed(2)}
                  </span>
                </div>
              )}
              <div className="flex justify-between font-medium pt-2 border-t border-gray-200 dark:border-gray-600">
                <span className="dark:text-gray-100">{isClient ? t("orders.total") : "Total"}</span>
                <span className="dark:text-gray-100 flex items-center gap-1">
                  <RiyalSymbol size={14} className="text-gray-800 dark:text-gray-100" />
                  {(order.total || 0).toFixed(2)}
                </span>
              </div>
              <div className="flex justify-between text-sm pt-2">
                <span className="text-gray-600 dark:text-gray-400">{isClient ? t("orders.paymentMethod") : "Payment Method"}</span>
                <span className="dark:text-gray-100">{getPaymentMethodLabel(order.paymentMethod)}</span>
              </div>
            </div>
          </div>

          {/* Status Update */}
          <div className="bg-gray-50 dark:bg-[#242832] rounded-lg p-4">
            <h3 className="text-lg font-medium mb-3 dark:text-gray-100">
              {isClient ? t("admin.updateStatus") : "Update Order Status"}
            </h3>
            <div className="flex items-center gap-3">
              <Select value={selectedStatus} onValueChange={(value) => setSelectedStatus(value as OrderStatus)}>
                <SelectTrigger className="flex-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={OrderStatus.ORDER_PLACED}>{getOrderStatusLabel(OrderStatus.ORDER_PLACED)}</SelectItem>
                  <SelectItem value={OrderStatus.PREPARING}>{getOrderStatusLabel(OrderStatus.PREPARING)}</SelectItem>
                  <SelectItem value={OrderStatus.READY_FOR_PICKUP}>{getOrderStatusLabel(OrderStatus.READY_FOR_PICKUP)}</SelectItem>
                  <SelectItem value={OrderStatus.OUT_FOR_DELIVERY}>{getOrderStatusLabel(OrderStatus.OUT_FOR_DELIVERY)}</SelectItem>
                  <SelectItem value={OrderStatus.DELIVERED}>{getOrderStatusLabel(OrderStatus.DELIVERED)}</SelectItem>
                  <SelectItem value={OrderStatus.CANCELLED}>{getOrderStatusLabel(OrderStatus.CANCELLED)}</SelectItem>
                </SelectContent>
              </Select>
              <Button 
                onClick={handleStatusUpdate}
                disabled={isUpdating || selectedStatus === order.status}
                className="bg-[#56999B] hover:bg-[#74C8CA] text-white"
              >
                {isUpdating ? (
                  <>
                    <i className="fa-solid fa-spinner fa-spin mr-2"></i>
                    {isClient ? t("common.updating") : "Updating..."}
                  </>
                ) : (
                  <>
                    <i className="fa-solid fa-save mr-2"></i>
                    {isClient ? t("admin.updateStatus") : "Update Status"}
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-600">
            <Button variant="outline" onClick={onClose}>
              {isClient ? t("common.close") : "Close"}
            </Button>
            {order.status !== OrderStatus.CANCELLED && order.status !== OrderStatus.DELIVERED && (
              <Button
                onClick={handleCancelOrder}
                variant="outline"
                className="border-red-300 text-red-600 hover:bg-red-50 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900/20"
              >
                <i className="fa-solid fa-ban mr-2"></i>
                {isClient ? t("orders.cancelOrder") : "Cancel Order"}
              </Button>
            )}
            <Button
              onClick={handlePrintReceipt}
              className="bg-[#83EAED] hover:bg-[#83EAED]/90 text-white"
            >
              <i className="fa-solid fa-print mr-2"></i>
              {isClient ? t("orders.printReceipt") : "Print Receipt"}
            </Button>
          </div>
        </div>
      </DialogContent>

      {/* Receipt Modal */}
      <ReceiptModal
        order={order}
        isOpen={isReceiptModalOpen}
        onClose={() => setIsReceiptModalOpen(false)}
      />

      {/* Cancel Order Modal */}
      <CancelOrderModal
        order={order}
        isOpen={isCancelModalOpen}
        onClose={() => setIsCancelModalOpen(false)}
        onOrderCancelled={handleOrderCancelled}
        userType="admin"
      />
    </Dialog>
  );
}
