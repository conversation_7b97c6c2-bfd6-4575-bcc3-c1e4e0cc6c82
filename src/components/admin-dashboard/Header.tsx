"use client";

import { useLocale } from "@/contexts/LocaleContext";
import LanguageSwitcher from "@/components/ui/LanguageSwitcher";
import DarkModeToggle from "@/components/ui/DarkModeToggle";
import Logo from "@/components/ui/Logo";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";
import { useState } from "react";

interface HeaderProps {
  toggleSidebar: () => void;
}

export default function AdminHeader({ toggleSidebar }: HeaderProps) {
  const { t, dir, isClient } = useLocale();
  const { logout } = useAuth();
  const router = useRouter();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  
  const handleLogout = async () => {
    try {
      await logout();
      // After logout, redirect to signin page
      router.push('/signin');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <header className="sticky top-0 z-30 bg-white dark:bg-[#1d2127] shadow-sm">
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <button 
              onClick={toggleSidebar}
              className="md:hidden text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 focus:outline-none"
              aria-label="Toggle sidebar"
            >
              <i className="fa-solid fa-bars text-xl"></i>
            </button>
            
            <Logo linkTo="/admin/dashboard" />
          </div>
          
          <div className="flex items-center space-x-4">
            <DarkModeToggle />
            <LanguageSwitcher />
            
            <div className="relative">
              <button 
                className="flex items-center gap-2 focus:outline-none"
                onClick={() => setDropdownOpen(!dropdownOpen)}
                onBlur={() => setTimeout(() => setDropdownOpen(false), 100)}
              >
                <div className="w-8 h-8 rounded-full bg-[#83EAED] dark:bg-[#5DBDC0] text-white flex items-center justify-center">
                  <i className="fa-solid fa-user-tie text-sm"></i>
                </div>
                <span className="text-gray-700 dark:text-gray-300 hidden sm:inline">Admin</span>
                <i className="fa-solid fa-chevron-down text-xs text-gray-500 dark:text-gray-400"></i>
              </button>
              
              {dropdownOpen && (
                <div className={`absolute ${dir === 'rtl' ? 'right-0' : 'left-0'} mt-2 w-48 bg-white dark:bg-[#1d2127] rounded-lg shadow-lg py-2 z-50`}>
                  <button 
                    onClick={handleLogout}
                    className="w-full text-left block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-[#242832]"
                  >
                    <i className="fa-solid fa-right-from-bracket mr-2"></i>
                    {isClient ? t('common.signOut') : 'Sign Out'}
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
} 