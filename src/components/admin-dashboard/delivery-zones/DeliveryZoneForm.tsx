"use client";

import { useState, useEffect } from "react";
import { useLocale } from "@/contexts/LocaleContext";
import { useAuth } from "@/hooks/useAuth";
import { DeliveryZone, DeliveryZoneType } from "@/types/models";
import { createDeliveryZone, updateDeliveryZone } from "@/lib/firebase/firestore";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Switch } from "@/components/ui/switch";
import { 
  Card,
  CardContent,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import RiyalSymbol from "@/components/ui/RiyalSymbol";

interface DeliveryZoneFormProps {
  editingZone: DeliveryZone | null;
  onZoneAdded: (zone: DeliveryZone) => void;
  onZoneUpdated: (zone: DeliveryZone) => void;
  onCancel: () => void;
}

export default function DeliveryZoneForm({ 
  editingZone, 
  onZoneAdded, 
  onZoneUpdated, 
  onCancel 
}: DeliveryZoneFormProps) {
  const { t, isClient } = useLocale();
  const { user } = useAuth();
  
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [zoneType, setZoneType] = useState<DeliveryZoneType>(DeliveryZoneType.PICK_UP);
  const [isActive, setIsActive] = useState(true);
  
  // Delivery specific fields
  const [deliveryFee, setDeliveryFee] = useState<number | undefined>(0);
  const [minOrderAmount, setMinOrderAmount] = useState<number | undefined>(0);
  const [radius, setRadius] = useState<number | undefined>(5);
  const [estimatedDeliveryTime, setEstimatedDeliveryTime] = useState<number | undefined>(30);
  
  // In-House Tables specific fields
  const [tableNumbers, setTableNumbers] = useState<string[]>([]);
  const [newTableNumber, setNewTableNumber] = useState("");
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    if (editingZone) {
      setName(editingZone.name);
      setDescription(editingZone.description || "");
      setZoneType(editingZone.type);
      setIsActive(editingZone.isActive);
      
      if (editingZone.type === DeliveryZoneType.DELIVERY) {
        setDeliveryFee(editingZone.deliveryFee);
        setMinOrderAmount(editingZone.minOrderAmount);
        setRadius(editingZone.radius);
        setEstimatedDeliveryTime(editingZone.estimatedDeliveryTime);
      } else if (editingZone.type === DeliveryZoneType.IN_HOUSE_TABLES) {
        setTableNumbers(editingZone.tableNumbers || []);
      }
    } else {
      resetForm();
    }
  }, [editingZone]);

  const resetForm = () => {
    setName("");
    setDescription("");
    setZoneType(DeliveryZoneType.PICK_UP);
    setIsActive(true);
    setDeliveryFee(0);
    setMinOrderAmount(0);
    setRadius(5);
    setEstimatedDeliveryTime(30);
    setTableNumbers([]);
    setNewTableNumber("");
    setError("");
  };

  const handleAddTableNumber = () => {
    if (newTableNumber.trim() === "") return;
    
    // Check if table number already exists
    if (tableNumbers.includes(newTableNumber.trim())) {
      setError(isClient ? t('admin.tableNumberExists') : "Table number already exists");
      return;
    }
    
    setTableNumbers([...tableNumbers, newTableNumber.trim()]);
    setNewTableNumber("");
    setError("");
  };

  const handleRemoveTableNumber = (tableNumber: string) => {
    setTableNumbers(tableNumbers.filter(t => t !== tableNumber));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) {
      setError(isClient ? t('common.notLoggedIn') : "You must be logged in");
      return;
    }
    
    if (!name.trim()) {
      setError(isClient ? t('admin.nameRequired') : "Name is required");
      return;
    }
    
    if (zoneType === DeliveryZoneType.IN_HOUSE_TABLES && tableNumbers.length === 0) {
      setError(isClient ? t('admin.tableNumbersRequired') : "At least one table number is required");
      return;
    }
    
    try {
      setLoading(true);
      setError("");
      
      const zoneData: Omit<DeliveryZone, 'id'> = {
        userId: user.uid,
        name: name.trim(),
        description: description.trim() || undefined,
        type: zoneType,
        isActive,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      
      // Add type-specific fields
      if (zoneType === DeliveryZoneType.DELIVERY) {
        zoneData.deliveryFee = deliveryFee;
        zoneData.minOrderAmount = minOrderAmount;
        zoneData.radius = radius;
        zoneData.estimatedDeliveryTime = estimatedDeliveryTime;
      } else if (zoneType === DeliveryZoneType.IN_HOUSE_TABLES) {
        zoneData.tableNumbers = tableNumbers;
      }
      
      if (editingZone) {
        const updatedZone = await updateDeliveryZone(editingZone.id, {
          ...zoneData,
          updatedAt: new Date(),
        });
        onZoneUpdated(updatedZone);
      } else {
        const newZone = await createDeliveryZone(zoneData);
        onZoneAdded(newZone);
        resetForm();
      }
    } catch (err) {
      console.error("Error saving delivery zone:", err);
      setError(isClient ? t('admin.errorSavingZone') : "Error saving delivery zone");
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 p-3 rounded-md text-sm">
          {error}
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <Label htmlFor="name">
              {isClient ? t('admin.zoneName') : 'Zone Name'} *
            </Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder={isClient ? t('admin.zoneNamePlaceholder') : "Enter zone name"}
              className="mt-1"
            />
          </div>
          
          <div>
            <Label htmlFor="description">
              {isClient ? t('admin.deliveryZoneDescription') : 'Description'}
            </Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setDescription(e.target.value)}
              placeholder={isClient ? t('admin.descriptionPlaceholder') : "Enter description (optional)"}
              className="mt-1"
              rows={3}
            />
          </div>
          
          <div className="space-y-2">
            <Label>
              {isClient ? t('admin.zoneType') : 'Zone Type'} *
            </Label>
            <RadioGroup 
              value={zoneType} 
              onValueChange={(value: string) => setZoneType(value as DeliveryZoneType)}
              className="flex flex-col space-y-2 mt-1"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value={DeliveryZoneType.PICK_UP} id="pickup" />
                <Label htmlFor="pickup" className="cursor-pointer">
                  {isClient ? t('admin.deliveryZoneTypes.pickUp') : 'Pick Up'}
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value={DeliveryZoneType.DELIVERY} id="delivery" />
                <Label htmlFor="delivery" className="cursor-pointer">
                  {isClient ? t('admin.deliveryZoneTypes.delivery') : 'Delivery'}
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value={DeliveryZoneType.IN_HOUSE_TABLES} id="tables" />
                <Label htmlFor="tables" className="cursor-pointer">
                  {isClient ? t('admin.deliveryZoneTypes.inHouseTables') : 'In-House Tables'}
                </Label>
              </div>
            </RadioGroup>
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch 
              id="isActive" 
              checked={isActive} 
              onCheckedChange={setIsActive} 
            />
            <Label htmlFor="isActive" className="cursor-pointer">
              {isClient ? t('admin.deliveryZoneActive') : 'Active'}
            </Label>
          </div>
        </div>
        
        <div>
          {/* Conditional fields based on zone type */}
          {zoneType === DeliveryZoneType.DELIVERY && (
            <Card>
              <CardContent className="pt-6 space-y-4">
                <h3 className="font-medium text-gray-800 dark:text-gray-200">
                  {isClient ? t('admin.deliverySettings') : 'Delivery Settings'}
                </h3>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="deliveryFee">
                      {isClient ? t('admin.deliveryFee') : 'Delivery Fee'}
                    </Label>
                    <div className="relative mt-1">
                      <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500 dark:text-gray-400">
                        <RiyalSymbol size={14} className="text-gray-500 dark:text-gray-400" />
                      </span>
                      <Input
                        id="deliveryFee"
                        type="number"
                        min="0"
                        step="0.01"
                        value={deliveryFee || ""}
                        onChange={(e) => setDeliveryFee(e.target.value ? parseFloat(e.target.value) : undefined)}
                        className="pl-12"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="minOrderAmount">
                      {isClient ? t('admin.minOrder') : 'Min Order Amount'}
                    </Label>
                    <div className="relative mt-1">
                      <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500 dark:text-gray-400">
                        <RiyalSymbol size={14} className="text-gray-500 dark:text-gray-400" />
                      </span>
                      <Input
                        id="minOrderAmount"
                        type="number"
                        min="0"
                        step="0.01"
                        value={minOrderAmount || ""}
                        onChange={(e) => setMinOrderAmount(e.target.value ? parseFloat(e.target.value) : undefined)}
                        className="pl-12"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="radius">
                      {isClient ? t('admin.radius') : 'Radius (km)'}
                    </Label>
                    <Input
                      id="radius"
                      type="number"
                      min="1"
                      value={radius || ""}
                      onChange={(e) => setRadius(e.target.value ? parseFloat(e.target.value) : undefined)}
                      className="mt-1"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="estimatedTime">
                      {isClient ? t('admin.estimatedTime') : 'Est. Time (min)'}
                    </Label>
                    <Input
                      id="estimatedTime"
                      type="number"
                      min="1"
                      value={estimatedDeliveryTime || ""}
                      onChange={(e) => setEstimatedDeliveryTime(e.target.value ? parseFloat(e.target.value) : undefined)}
                      className="mt-1"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
          
          {zoneType === DeliveryZoneType.IN_HOUSE_TABLES && (
            <Card>
              <CardContent className="pt-6 space-y-4">
                <h3 className="font-medium text-gray-800 dark:text-gray-200">
                  {isClient ? t('admin.tableSettings') : 'Table Settings'}
                </h3>
                
                <div className="space-y-4">
                  <div className="flex space-x-2">
                    <Input
                      value={newTableNumber}
                      onChange={(e) => setNewTableNumber(e.target.value)}
                      placeholder={isClient ? t('admin.tableNumberPlaceholder') : "Enter table number"}
                      className="flex-1"
                    />
                    <Button 
                      type="button" 
                      onClick={handleAddTableNumber}
                      variant="secondary"
                    >
                      <i className="fa-solid fa-plus mr-1"></i>
                      {isClient ? t('admin.addTable') : 'Add'}
                    </Button>
                  </div>
                  
                  <div>
                    <Label>
                      {isClient ? t('admin.tables') : 'Tables'}
                      {tableNumbers.length > 0 && (
                        <span className="ml-2 text-xs text-gray-500 dark:text-gray-400">
                          ({tableNumbers.length})
                        </span>
                      )}
                    </Label>
                    
                    {tableNumbers.length === 0 ? (
                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                        {isClient ? t('admin.noTablesAdded') : 'No tables added yet'}
                      </p>
                    ) : (
                      <div className="flex flex-wrap gap-2 mt-2">
                        {tableNumbers.map((table) => (
                          <Badge 
                            key={table} 
                            variant="secondary"
                            className="flex items-center gap-1 px-2 py-1"
                          >
                            {table}
                            <button
                              type="button"
                              onClick={() => handleRemoveTableNumber(table)}
                              className="ml-1 text-gray-500 hover:text-red-500 dark:text-gray-400 dark:hover:text-red-400"
                            >
                              <i className="fa-solid fa-xmark text-xs"></i>
                            </button>
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
      
      <div className="flex justify-end space-x-3">
        {editingZone && (
          <Button 
            type="button" 
            variant="outline" 
            onClick={onCancel}
            disabled={loading}
          >
            {isClient ? t('common.cancel') : 'Cancel'}
          </Button>
        )}
        <Button 
          type="submit" 
          disabled={loading}
          className="bg-[#56999B] hover:bg-[#56999B]/90 dark:bg-[#5DBDC0] dark:hover:bg-[#5DBDC0]/90"
        >
          {loading && <i className="fa-solid fa-spinner fa-spin mr-2"></i>}
          {editingZone
            ? isClient ? t('admin.updateZone') : 'Update Zone'
            : isClient ? t('admin.addZone') : 'Add Zone'
          }
        </Button>
      </div>
    </form>
  );
}
