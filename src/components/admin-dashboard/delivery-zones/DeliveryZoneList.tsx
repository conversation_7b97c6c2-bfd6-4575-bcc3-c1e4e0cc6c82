"use client";

import { useState } from "react";
import { useLocale } from "@/contexts/LocaleContext";
import { DeliveryZone, DeliveryZoneType } from "@/types/models";
import { deleteDeliveryZone } from "@/lib/firebase/firestore";
import { useAuth } from "@/hooks/useAuth";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Loader2, Pencil, Trash2 } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import RiyalSymbol from "@/components/ui/RiyalSymbol";

interface DeliveryZoneListProps {
  zones: DeliveryZone[];
  loading: boolean;
  onEdit: (zone: DeliveryZone) => void;
  onDelete: (zoneId: string) => void;
}

export default function DeliveryZoneList({ zones, loading, onEdit, onDelete }: DeliveryZoneListProps) {
  const { t, isClient } = useLocale();
  const { user } = useAuth();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [zoneToDelete, setZoneToDelete] = useState<DeliveryZone | null>(null);
  const [deletingId, setDeletingId] = useState("");

  const handleDeleteClick = (zone: DeliveryZone) => {
    setZoneToDelete(zone);
    setDeleteDialogOpen(true);
  };

  const handleDelete = async () => {
    if (!user || !zoneToDelete) return;
    
    try {
      setDeletingId(zoneToDelete.id);
      await deleteDeliveryZone(zoneToDelete.id);
      onDelete(zoneToDelete.id);
      setDeleteDialogOpen(false);
    } catch (error) {
      console.error("Error deleting delivery zone:", error);
    } finally {
      setDeletingId("");
      setZoneToDelete(null);
    }
  };

  const getZoneTypeBadge = (type: DeliveryZoneType) => {
    switch (type) {
      case DeliveryZoneType.PICK_UP:
        return (
          <Badge variant="info" className="capitalize">
            {isClient ? t('admin.deliveryZoneTypes.pickUp') : 'Pick Up'}
          </Badge>
        );
      case DeliveryZoneType.DELIVERY:
        return (
          <Badge variant="success" className="capitalize">
            {isClient ? t('admin.deliveryZoneTypes.delivery') : 'Delivery'}
          </Badge>
        );
      case DeliveryZoneType.IN_HOUSE_TABLES:
        return (
          <Badge variant="warning" className="capitalize">
            {isClient ? t('admin.deliveryZoneTypes.inHouseTables') : 'In-House Tables'}
          </Badge>
        );
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div 
          data-testid="loading-spinner"
          className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#56999B] dark:border-[#5DBDC0]" 
        />
      </div>
    );
  }

  if (zones.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <i className="fa-solid fa-truck text-5xl text-gray-300 dark:text-gray-600 mb-4"></i>
        <p className="text-gray-500 dark:text-gray-400 text-center max-w-md">
          {isClient ? t('admin.noZonesMessage') : 'No delivery zones available yet. Add your first zone below.'}
        </p>
      </div>
    );
  }

  return (
    <>
      <div className="overflow-x-auto">
        <table className="w-full text-sm text-left">
          <thead className="text-xs text-gray-700 dark:text-gray-300 uppercase bg-gray-50 dark:bg-[#242832]">
            <tr>
              <th scope="col" className="px-6 py-3">
                {isClient ? t('admin.zoneName') : 'Zone Name'}
              </th>
              <th scope="col" className="px-6 py-3">
                {isClient ? t('admin.zoneType') : 'Type'}
              </th>
              <th scope="col" className="px-6 py-3">
                {isClient ? t('admin.zoneDetails') : 'Details'}
              </th>
              <th scope="col" className="px-6 py-3">
                {isClient ? t('admin.deliveryZoneStatus') : 'Status'}
              </th>
              <th scope="col" className="px-6 py-3 text-right">
                {isClient ? t('admin.deliveryZoneActions') : 'Actions'}
              </th>
            </tr>
          </thead>
          <tbody>
            {zones.map((zone) => (
              <tr 
                key={zone.id} 
                className="bg-white dark:bg-[#1d2127] border-b border-gray-200 dark:border-gray-700"
              >
                <td className="px-6 py-4 font-medium text-gray-900 dark:text-white">
                  {zone.name}
                </td>
                <td className="px-6 py-4">
                  {getZoneTypeBadge(zone.type)}
                </td>
                <td className="px-6 py-4">
                  {zone.type === DeliveryZoneType.DELIVERY && (
                    <div>
                      <p className="text-gray-600 dark:text-gray-400 flex items-center gap-1">
                        {isClient ? `${t('admin.deliveryFee')}: ` : `Delivery Fee: `}
                        <RiyalSymbol size={12} className="text-gray-600 dark:text-gray-400" />
                        {zone.deliveryFee?.toFixed(2)}
                      </p>
                      {zone.minOrderAmount && (
                        <p className="text-gray-600 dark:text-gray-400 flex items-center gap-1">
                          {isClient ? `${t('admin.minOrder')}: ` : `Min Order: `}
                          <RiyalSymbol size={12} className="text-gray-600 dark:text-gray-400" />
                          {zone.minOrderAmount.toFixed(2)}
                        </p>
                      )}
                    </div>
                  )}
                  {zone.type === DeliveryZoneType.IN_HOUSE_TABLES && zone.tableNumbers && (
                    <div>
                      <p className="text-gray-600 dark:text-gray-400">
                        {isClient ? `${t('admin.tables')}: ${zone.tableNumbers.join(', ')}` : `Tables: ${zone.tableNumbers.join(', ')}`}
                      </p>
                    </div>
                  )}
                  {zone.description && (
                    <p className="text-gray-600 dark:text-gray-400 mt-1 text-xs">
                      {zone.description}
                    </p>
                  )}
                </td>
                <td className="px-6 py-4">
                  {zone.isActive ? (
                    <Badge variant="outline" className="bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-300 border-green-200 dark:border-green-800">
                      {isClient ? t('admin.deliveryZoneActive') : 'Active'}
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-gray-50 text-gray-700 dark:bg-gray-900/30 dark:text-gray-300 border-gray-200 dark:border-gray-800">
                      {isClient ? t('admin.deliveryZoneInactive') : 'Inactive'}
                    </Badge>
                  )}
                </td>
                <td className="px-6 py-4 text-right space-x-2">
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => onEdit(zone)}
                    className="text-[#56999B] dark:text-[#5DBDC0] cursor-pointer hover:text-[#56999B]/80 dark:hover:text-[#5DBDC0]/80"
                  >
                    <i className="fa-solid fa-pen-to-square mr-1"></i>
                    {isClient ? t('admin.deliveryZoneEdit') : 'Edit'}
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => handleDeleteClick(zone)}
                    className="text-red-500 cursor-pointer hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                  >
                    <i className="fa-solid fa-trash-can mr-1"></i>
                    {isClient ? t('admin.deliveryZoneDelete') : 'Delete'}
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {isClient ? t('admin.confirmDelete') : 'Confirm Delete'}
            </AlertDialogTitle>
            <AlertDialogDescription>
                {isClient 
                  ? t('admin.deleteZoneConfirmation', { name: zoneToDelete?.name || '' }) 
                  : `Are you sure you want to delete "${zoneToDelete?.name || ''}"? This action cannot be undone.`}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>
              {isClient ? t('common.cancel') : 'Cancel'}
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-red-500 text-white hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700">
              {isClient ? t('common.deleteButton') : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
