export interface CountryCode {
  code: string;
  name: string;
  nameAr: string;
  dialCode: string;
  flag: string;
  format?: string;
  minLength?: number;
  maxLength?: number;
}

export const COUNTRY_CODES: CountryCode[] = [
  // Saudi Arabia (default)
  {
    code: 'SA',
    name: 'Saudi Arabia',
    nameAr: 'المملكة العربية السعودية',
    dialCode: '+966',
    flag: '🇸🇦',
    format: '5X XXX XXXX',
    minLength: 9,
    maxLength: 9
  },
  // Popular Middle East countries
  {
    code: 'AE',
    name: 'United Arab Emirates',
    nameAr: 'الإمارات العربية المتحدة',
    dialCode: '+971',
    flag: '🇦🇪',
    format: '5X XXX XXXX',
    minLength: 9,
    maxLength: 9
  },
  {
    code: 'QA',
    name: 'Qatar',
    nameAr: 'قطر',
    dialCode: '+974',
    flag: '🇶🇦',
    format: 'XXXX XXXX',
    minLength: 8,
    maxLength: 8
  },
  {
    code: 'KW',
    name: 'Kuwait',
    nameAr: 'الكويت',
    dialCode: '+965',
    flag: '🇰🇼',
    format: 'XXXX XXXX',
    minLength: 8,
    maxLength: 8
  },
  {
    code: 'BH',
    name: 'Bahrain',
    nameAr: 'البحرين',
    dialCode: '+973',
    flag: '🇧🇭',
    format: 'XXXX XXXX',
    minLength: 8,
    maxLength: 8
  },
  {
    code: 'OM',
    name: 'Oman',
    nameAr: 'عُمان',
    dialCode: '+968',
    flag: '🇴🇲',
    format: 'XXXX XXXX',
    minLength: 8,
    maxLength: 8
  },
  {
    code: 'JO',
    name: 'Jordan',
    nameAr: 'الأردن',
    dialCode: '+962',
    flag: '🇯🇴',
    format: 'X XXXX XXXX',
    minLength: 9,
    maxLength: 9
  },
  {
    code: 'LB',
    name: 'Lebanon',
    nameAr: 'لبنان',
    dialCode: '+961',
    flag: '🇱🇧',
    format: 'XX XXX XXX',
    minLength: 8,
    maxLength: 8
  },
  {
    code: 'EG',
    name: 'Egypt',
    nameAr: 'مصر',
    dialCode: '+20',
    flag: '🇪🇬',
    format: '1X XXXX XXXX',
    minLength: 10,
    maxLength: 10
  },
  // Major international countries
  {
    code: 'US',
    name: 'United States',
    nameAr: 'الولايات المتحدة',
    dialCode: '+1',
    flag: '🇺🇸',
    format: '(XXX) XXX-XXXX',
    minLength: 10,
    maxLength: 10
  },
  {
    code: 'GB',
    name: 'United Kingdom',
    nameAr: 'المملكة المتحدة',
    dialCode: '+44',
    flag: '🇬🇧',
    format: 'XXXX XXX XXX',
    minLength: 10,
    maxLength: 11
  },
  {
    code: 'CA',
    name: 'Canada',
    nameAr: 'كندا',
    dialCode: '+1',
    flag: '🇨🇦',
    format: '(XXX) XXX-XXXX',
    minLength: 10,
    maxLength: 10
  },
  {
    code: 'AU',
    name: 'Australia',
    nameAr: 'أستراليا',
    dialCode: '+61',
    flag: '🇦🇺',
    format: 'XXX XXX XXX',
    minLength: 9,
    maxLength: 9
  },
  {
    code: 'DE',
    name: 'Germany',
    nameAr: 'ألمانيا',
    dialCode: '+49',
    flag: '🇩🇪',
    format: 'XXX XXXXXXX',
    minLength: 10,
    maxLength: 12
  },
  {
    code: 'FR',
    name: 'France',
    nameAr: 'فرنسا',
    dialCode: '+33',
    flag: '🇫🇷',
    format: 'X XX XX XX XX',
    minLength: 9,
    maxLength: 9
  },
  {
    code: 'IT',
    name: 'Italy',
    nameAr: 'إيطاليا',
    dialCode: '+39',
    flag: '🇮🇹',
    format: 'XXX XXX XXXX',
    minLength: 9,
    maxLength: 10
  },
  {
    code: 'ES',
    name: 'Spain',
    nameAr: 'إسبانيا',
    dialCode: '+34',
    flag: '🇪🇸',
    format: 'XXX XXX XXX',
    minLength: 9,
    maxLength: 9
  },
  {
    code: 'NL',
    name: 'Netherlands',
    nameAr: 'هولندا',
    dialCode: '+31',
    flag: '🇳🇱',
    format: 'X XXXX XXXX',
    minLength: 9,
    maxLength: 9
  },
  {
    code: 'IN',
    name: 'India',
    nameAr: 'الهند',
    dialCode: '+91',
    flag: '🇮🇳',
    format: 'XXXXX XXXXX',
    minLength: 10,
    maxLength: 10
  },
  {
    code: 'PK',
    name: 'Pakistan',
    nameAr: 'باكستان',
    dialCode: '+92',
    flag: '🇵🇰',
    format: 'XXX XXXXXXX',
    minLength: 10,
    maxLength: 10
  },
  {
    code: 'BD',
    name: 'Bangladesh',
    nameAr: 'بنغلاديش',
    dialCode: '+880',
    flag: '🇧🇩',
    format: 'XXXX XXXXXX',
    minLength: 10,
    maxLength: 10
  },
  {
    code: 'PH',
    name: 'Philippines',
    nameAr: 'الفلبين',
    dialCode: '+63',
    flag: '🇵🇭',
    format: 'XXX XXX XXXX',
    minLength: 10,
    maxLength: 10
  },
  {
    code: 'MY',
    name: 'Malaysia',
    nameAr: 'ماليزيا',
    dialCode: '+60',
    flag: '🇲🇾',
    format: 'XX XXXX XXXX',
    minLength: 9,
    maxLength: 10
  },
  {
    code: 'SG',
    name: 'Singapore',
    nameAr: 'سنغافورة',
    dialCode: '+65',
    flag: '🇸🇬',
    format: 'XXXX XXXX',
    minLength: 8,
    maxLength: 8
  },
  {
    code: 'TH',
    name: 'Thailand',
    nameAr: 'تايلاند',
    dialCode: '+66',
    flag: '🇹🇭',
    format: 'XX XXX XXXX',
    minLength: 9,
    maxLength: 9
  },
  {
    code: 'JP',
    name: 'Japan',
    nameAr: 'اليابان',
    dialCode: '+81',
    flag: '🇯🇵',
    format: 'XX XXXX XXXX',
    minLength: 10,
    maxLength: 11
  },
  {
    code: 'KR',
    name: 'South Korea',
    nameAr: 'كوريا الجنوبية',
    dialCode: '+82',
    flag: '🇰🇷',
    format: 'XX XXXX XXXX',
    minLength: 10,
    maxLength: 11
  },
  {
    code: 'CN',
    name: 'China',
    nameAr: 'الصين',
    dialCode: '+86',
    flag: '🇨🇳',
    format: 'XXX XXXX XXXX',
    minLength: 11,
    maxLength: 11
  },
  {
    code: 'TR',
    name: 'Turkey',
    nameAr: 'تركيا',
    dialCode: '+90',
    flag: '🇹🇷',
    format: 'XXX XXX XX XX',
    minLength: 10,
    maxLength: 10
  },
  {
    code: 'RU',
    name: 'Russia',
    nameAr: 'روسيا',
    dialCode: '+7',
    flag: '🇷🇺',
    format: 'XXX XXX-XX-XX',
    minLength: 10,
    maxLength: 10
  },
  {
    code: 'BR',
    name: 'Brazil',
    nameAr: 'البرازيل',
    dialCode: '+55',
    flag: '🇧🇷',
    format: 'XX XXXXX-XXXX',
    minLength: 10,
    maxLength: 11
  },
  {
    code: 'MX',
    name: 'Mexico',
    nameAr: 'المكسيك',
    dialCode: '+52',
    flag: '🇲🇽',
    format: 'XX XXXX XXXX',
    minLength: 10,
    maxLength: 10
  },
  {
    code: 'ZA',
    name: 'South Africa',
    nameAr: 'جنوب أفريقيا',
    dialCode: '+27',
    flag: '🇿🇦',
    format: 'XX XXX XXXX',
    minLength: 9,
    maxLength: 9
  }
];

// Default country (Saudi Arabia)
export const DEFAULT_COUNTRY = COUNTRY_CODES[0];

// Helper functions
export const getCountryByCode = (code: string): CountryCode | undefined => {
  return COUNTRY_CODES.find(country => country.code === code);
};

export const getCountryByDialCode = (dialCode: string): CountryCode | undefined => {
  return COUNTRY_CODES.find(country => country.dialCode === dialCode);
};

export const detectCountryFromPhoneNumber = (phoneNumber: string): CountryCode => {
  const cleaned = phoneNumber.replace(/\D/g, '');

  // Check for exact dial code matches (longest first to avoid conflicts)
  const sortedCountries = [...COUNTRY_CODES].sort((a, b) => b.dialCode.length - a.dialCode.length);

  for (const country of sortedCountries) {
    const dialCodeDigits = country.dialCode.replace(/\D/g, '');
    if (cleaned.startsWith(dialCodeDigits)) {
      return country;
    }
  }

  // Default to Saudi Arabia
  return DEFAULT_COUNTRY;
};

export const getPhoneNumberPlaceholder = (countryCode: CountryCode): string => {
  if (countryCode.format) {
    return `${countryCode.dialCode} ${countryCode.format}`;
  }
  return `${countryCode.dialCode} XXXXXXXXX`;
};
