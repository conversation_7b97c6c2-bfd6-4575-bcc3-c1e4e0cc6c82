import { 
  createUserWithEmailAndPassword, 
  signInWithEmailAndPassword,
  signInWithPopup,
  GoogleAuthProvider,
  signOut,
  UserCredential,
  User,
  sendEmailVerification,
  sendPasswordResetEmail,
  ActionCodeSettings
} from 'firebase/auth';
import { auth } from './config';
import Cookies from 'js-cookie';

// Constants
const SESSION_COOKIE_NAME = 'session';
const SESSION_EXPIRY_DAYS = 14;

// Function to get the current domain dynamically
const getCurrentDomain = (): string => {
  // Check if we're in the browser
  if (typeof window !== 'undefined') {
    return `${window.location.protocol}//${window.location.host}`;
  }

  // Fallback to environment variable or localhost for server-side
  return process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
};

// Function to get action code settings dynamically
const getActionCodeSettings = (): ActionCodeSettings => {
  const baseUrl = getCurrentDomain();

  return {
    // URL you want to redirect back to after email verification
    url: `${baseUrl}/auth/action`,
    // Handle the verification link in the app
    handleCodeInApp: true,
    // iOS app settings (if you have an iOS app)
    iOS: {
      bundleId: 'com.barcodecafe.app'
    },
    // Android app settings (if you have an Android app)
    android: {
      packageName: 'com.barcodecafe.app',
      installApp: true,
      minimumVersion: '12'
    }
  };
};

/**
 * Register a new user with email and password
 */
export const registerWithEmailAndPassword = async (
  email: string, 
  password: string
): Promise<UserCredential> => {
  return createUserWithEmailAndPassword(auth, email, password);
};

/**
 * Send email verification to user
 */
export const sendVerificationEmail = async (user: User): Promise<void> => {
  const actionCodeSettings = getActionCodeSettings();
  console.log('Sending verification email with settings:', actionCodeSettings);
  return sendEmailVerification(user, actionCodeSettings);
};

/**
 * Login with email and password
 */
export const loginWithEmailAndPassword = async (
  email: string, 
  password: string
): Promise<UserCredential> => {
  return signInWithEmailAndPassword(auth, email, password);
};

/**
 * Login with Google
 */
export const loginWithGoogle = async (): Promise<UserCredential> => {
  const provider = new GoogleAuthProvider();
  return signInWithPopup(auth, provider);
};

/**
 * Send password reset email
 */
export const resetPassword = async (email: string): Promise<void> => {
  return sendPasswordResetEmail(auth, email);
};

/**
 * Logout the current user
 */
export const logout = async (): Promise<void> => {
  Cookies.remove(SESSION_COOKIE_NAME);
  return signOut(auth);
};

/**
 * Set the session cookie with the user's ID token
 */
export const setSessionCookie = async (user: User): Promise<void> => {
  try {
    const idToken = await user.getIdToken();
    
    // Store the ID token in the session cookie
    Cookies.set(SESSION_COOKIE_NAME, idToken, { 
      expires: SESSION_EXPIRY_DAYS,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict'
    });
  } catch (error) {
    console.error('Error setting session cookie:', error);
    throw error;
  }
};

/**
 * Get the session cookie
 */
export const getSessionCookie = (): string | undefined => {
  return Cookies.get(SESSION_COOKIE_NAME);
};

/**
 * Remove the session cookie
 */
export const removeSessionCookie = (): void => {
  Cookies.remove(SESSION_COOKIE_NAME);
}; 