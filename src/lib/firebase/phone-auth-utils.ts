import {
  signInWith<PERSON>hone<PERSON>umber,
  RecaptchaVerifier,
  ConfirmationResult,
  UserCredential,
  signOut,
  User,
  updateProfile
} from 'firebase/auth';
import { auth } from './config';
import Cookies from 'js-cookie';
import { CountryCode, DEFAULT_COUNTRY, detectCountryFromPhoneNumber, getCountryByDialCode } from '../country-codes';

// Constants
const SESSION_COOKIE_NAME = 'session';
const SESSION_EXPIRY_DAYS = 14;

// Global variables for reCAPTCHA
let recaptchaVerifier: RecaptchaVerifier | null = null;
let confirmationResult: ConfirmationResult | null = null;

/**
 * Initialize reCAPTCHA verifier
 */
export const initializeRecaptcha = (containerId: string = 'recaptcha-container'): RecaptchaVerifier => {
  // Clean up existing verifier
  if (recaptchaVerifier) {
    try {
      recaptchaVerifier.clear();
    } catch (error) {
      console.warn('Error clearing existing reCAPTCHA verifier:', error);
    }
  }

  try {
    recaptchaVerifier = new RecaptchaVerifier(auth, containerId, {
      size: 'invisible', // Use invisible reCAPTCHA to avoid UI issues
      callback: (response: string) => {
        console.log('reCAPTCHA solved:', response);
      },
      'expired-callback': () => {
        console.log('reCAPTCHA expired');
        // Auto-retry on expiration
        if (recaptchaVerifier) {
          try {
            recaptchaVerifier.render();
          } catch (error) {
            console.error('Error re-rendering reCAPTCHA:', error);
          }
        }
      },
      'error-callback': (error: any) => {
        console.error('reCAPTCHA error:', error);
      }
    });

    return recaptchaVerifier;
  } catch (error) {
    console.error('Error initializing reCAPTCHA:', error);
    throw new Error('Failed to initialize reCAPTCHA. Please refresh the page and try again.');
  }
};

/**
 * Send SMS verification code to phone number
 */
export const sendPhoneVerificationCode = async (
  phoneNumber: string,
  recaptchaVerifier: RecaptchaVerifier,
  countryCode?: CountryCode
): Promise<ConfirmationResult> => {
  try {
    // Format phone number to international format
    const formattedPhoneNumber = formatInternationalPhoneNumber(phoneNumber, countryCode);

    console.log('Sending verification code to:', formattedPhoneNumber);

    confirmationResult = await signInWithPhoneNumber(auth, formattedPhoneNumber, recaptchaVerifier);

    console.log('Verification code sent successfully');
    return confirmationResult;
  } catch (error) {
    console.error('Error sending verification code:', error);
    throw error;
  }
};

/**
 * Verify SMS code and complete phone authentication
 */
export const verifyPhoneCode = async (
  code: string,
  confirmation?: ConfirmationResult
): Promise<UserCredential> => {
  try {
    const result = confirmation || confirmationResult;
    
    if (!result) {
      throw new Error('No confirmation result available. Please request a new verification code.');
    }

    console.log('Verifying phone code:', code);
    const userCredential = await result.confirm(code);
    
    console.log('Phone verification successful');
    return userCredential;
  } catch (error) {
    console.error('Error verifying phone code:', error);
    throw error;
  }
};

/**
 * Format phone number to international format with country code
 */
export const formatInternationalPhoneNumber = (phoneNumber: string, countryCode?: CountryCode): string => {
  // Remove all non-digit characters
  let cleaned = phoneNumber.replace(/\D/g, '');

  // If phone number already has a country code (starts with +)
  if (phoneNumber.startsWith('+')) {
    return phoneNumber.replace(/\s/g, ''); // Remove spaces but keep the format
  }

  // Use provided country code or detect from number
  const country = countryCode || detectCountryFromPhoneNumber(phoneNumber);
  const dialCodeDigits = country.dialCode.replace(/\D/g, '');

  // If number already starts with the dial code, add +
  if (cleaned.startsWith(dialCodeDigits)) {
    return '+' + cleaned;
  }

  // Handle Saudi-specific formats (backward compatibility)
  if (country.code === 'SA') {
    // If starts with 0, replace with +966
    if (cleaned.startsWith('0')) {
      return '+966' + cleaned.substring(1);
    }

    // If starts with 5 (Saudi mobile), add +966
    if (cleaned.startsWith('5') && cleaned.length === 9) {
      return '+966' + cleaned;
    }
  }

  // For other countries, add the dial code
  return country.dialCode + cleaned;
};

/**
 * Legacy function for backward compatibility
 * @deprecated Use formatInternationalPhoneNumber instead
 */
export const formatPhoneNumber = (phoneNumber: string): string => {
  return formatInternationalPhoneNumber(phoneNumber, DEFAULT_COUNTRY);
};

/**
 * Validate international phone number format
 */
export const validateInternationalPhoneNumber = (phoneNumber: string, countryCode?: CountryCode): boolean => {
  try {
    const formatted = formatInternationalPhoneNumber(phoneNumber, countryCode);
    const country = countryCode || detectCountryFromPhoneNumber(formatted);

    // Remove dial code to get local number
    const dialCodeDigits = country.dialCode.replace(/\D/g, '');
    const localNumber = formatted.replace('+' + dialCodeDigits, '');
    const localDigits = localNumber.replace(/\D/g, '');

    // Check length constraints if available
    if (country.minLength && localDigits.length < country.minLength) {
      return false;
    }

    if (country.maxLength && localDigits.length > country.maxLength) {
      return false;
    }

    // Basic validation: must have at least 7 digits for local number
    return localDigits.length >= 7;
  } catch (error) {
    return false;
  }
};

/**
 * Validate Saudi phone number format (backward compatibility)
 */
export const validateSaudiPhoneNumber = (phoneNumber: string): boolean => {
  return validateInternationalPhoneNumber(phoneNumber, DEFAULT_COUNTRY);
};



/**
 * Parse phone number and extract country and local parts
 */
export const parsePhoneNumber = (phoneNumber: string): { country: CountryCode; localNumber: string; formatted: string } => {
  const formatted = formatInternationalPhoneNumber(phoneNumber);
  const country = detectCountryFromPhoneNumber(formatted);
  const dialCodeDigits = country.dialCode.replace(/\D/g, '');
  const localNumber = formatted.replace('+' + dialCodeDigits, '');

  return {
    country,
    localNumber,
    formatted
  };
};

/**
 * Update user profile with display name
 */
export const updateUserDisplayName = async (user: User, displayName: string): Promise<void> => {
  try {
    await updateProfile(user, { displayName });
    console.log('User display name updated successfully');
  } catch (error) {
    console.error('Error updating user display name:', error);
    throw error;
  }
};

/**
 * Logout the current user
 */
export const logout = async (): Promise<void> => {
  Cookies.remove(SESSION_COOKIE_NAME);
  return signOut(auth);
};

/**
 * Set the session cookie with the user's ID token
 */
export const setSessionCookie = async (user: User): Promise<void> => {
  try {
    const idToken = await user.getIdToken();
    
    // Store the ID token in the session cookie
    Cookies.set(SESSION_COOKIE_NAME, idToken, { 
      expires: SESSION_EXPIRY_DAYS,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict'
    });
  } catch (error) {
    console.error('Error setting session cookie:', error);
    throw error;
  }
};

/**
 * Get the session cookie
 */
export const getSessionCookie = (): string | undefined => {
  return Cookies.get(SESSION_COOKIE_NAME);
};

/**
 * Remove the session cookie
 */
export const removeSessionCookie = (): void => {
  Cookies.remove(SESSION_COOKIE_NAME);
};

/**
 * Clean up reCAPTCHA verifier
 */
export const cleanupRecaptcha = (): void => {
  try {
    if (recaptchaVerifier) {
      recaptchaVerifier.clear();
      recaptchaVerifier = null;
    }
    confirmationResult = null;
  } catch (error) {
    console.warn('Error cleaning up reCAPTCHA:', error);
    // Force reset even if cleanup fails
    recaptchaVerifier = null;
    confirmationResult = null;
  }
};

/**
 * Check if phone number is already registered
 */
export const checkPhoneNumberExists = async (phoneNumber: string): Promise<boolean> => {
  try {
    // This is a workaround since Firebase doesn't provide a direct way to check phone number existence
    // We'll try to get user profile from Firestore by phone number
    const { getUserProfileByPhoneNumber } = await import('./firestore');
    const profile = await getUserProfileByPhoneNumber(formatPhoneNumber(phoneNumber));
    return profile !== null;
  } catch (error) {
    console.error('Error checking phone number existence:', error);
    return false;
  }
};
