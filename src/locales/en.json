{"common": {"barcodeCafe": "Barcode Cafe", "footer": "2025 Barcode Cafe. All rights reserved.", "allRightsReserved": "All rights reserved.", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "signOut": "Sign Out", "logout": "Logout", "cancel": "Cancel", "search": "Search", "loading": "Loading...", "updating": "Updating...", "generating": "Generating...", "deleting": "Deleting...", "cafeTagline": "Barcode Speciality Coffee", "cafeDescription": "Premium coffee and delicious treats in the heart of KSA", "close": "Close", "kcal": "kcal", "done": "Done", "remove": "Remove", "currency": "SAR", "min": "min", "back": "Back", "deleteButton": "Delete", "caffeine": "caffeine", "allergic": "allergens", "saving": "Saving..."}, "signin": {"title": "Welcome Back!", "subtitle": "Sign in to your account", "emailLabel": "Email address", "emailPlaceholder": "Enter your email", "passwordLabel": "Password", "passwordPlaceholder": "Enter your password", "forgotPassword": "Forgot password?", "signInButton": "Sign In", "orSignInWith": "Or sign in with", "noAccount": "Don't have an account?", "signUp": "Sign up now", "rememberMe": "Remember me"}, "signup": {"title": "Join the Barcode Cafe Community!", "subtitle": "Create your account to get started", "fullNameLabel": "Full Name", "fullNamePlaceholder": "Enter your full name", "emailLabel": "Email Address", "emailPlaceholder": "Enter your email", "passwordLabel": "Password", "passwordPlaceholder": "Create a password", "confirmPasswordLabel": "Confirm Password", "confirmPasswordPlaceholder": "Confirm your password", "termsCheckbox": "I agree to the", "termsLink": "Terms & Conditions", "createAccountButton": "Create Account", "orSignUpWith": "Or sign up with", "haveAccount": "Already have an account?", "signIn": "Sign in here"}, "phoneAuth": {"title": "Welcome to BarcodeCafe", "subtitle": "Sign in with your phone number", "error": "Error", "phoneRequired": "Please enter your phone number", "codeRequired": "Please enter the verification code", "phoneStep": {"title": "Enter Phone Number", "description": "We'll send you a verification code via SMS"}, "codeStep": {"title": "Verify Code", "description": "Enter the 6-digit code sent to {{phoneNumber}}"}, "profileStep": {"title": "Complete Profile", "description": "Please provide your name to complete registration"}, "phoneLabel": "Phone Number", "phonePlaceholder": "05XXXXXXXX or +966 5X XXX XXXX", "phoneHelp": "Enter your Saudi mobile number", "internationalHelp": "Enter your phone number with country code", "searchCountry": "Search countries...", "noCountriesFound": "No countries found", "codeLabel": "Verification Code", "codePlaceholder": "Enter 6-digit code", "nameLabel": "Your Name (Optional)", "namePlaceholder": "Enter your full name", "nameHelp": "This will be used for your orders and profile", "sendCodeButton": "Send Verification Code", "verifyButton": "Verify", "backButton": "Back", "resendCode": "Didn't receive the code? <PERSON>send", "verificationComplete": "Verification Complete!", "redirecting": "Redirecting you to the dashboard...", "continueManually": "Continue to Dashboard", "redirectIssue": "If redirect doesn't work automatically, click the button above", "termsText": "By continuing, you agree to our Terms of Service and Privacy Policy"}, "nav": {"dashboard": "Dashboard", "orderHistory": "Order History", "loyalty": "Loyalty Program", "giftCards": "Gift Cards", "addresses": "Addresses", "reviews": "Reviews", "settings": "Settings", "menu": "<PERSON><PERSON>"}, "menu": {"title": "Our Menu", "description": "Description", "categories": "Categories", "categoryDescription": "Browse our selection by category", "noItems": "No menu items found in this category.", "featured": "Featured Item", "stockStatus": {"inStock": "In Stock", "lowStock": "Low Stock", "outOfStock": "Out of Stock"}, "cart": "<PERSON><PERSON>", "viewCart": "View Cart", "addToCart": "Add to Cart", "addedToCart": "Added to <PERSON><PERSON>", "add": "Add", "quantity": "Quantity", "total": "Total", "subtotal": "Subtotal", "clearCart": "Clear Cart", "placeOrder": "Place Order", "placingOrder": "Placing Order...", "emptyCart": "Your cart is empty", "searchResults": "Search Results", "noSearchResults": "No results found. Try a different search term.", "availableForDelivery": "Available for delivery", "pickupOnly": "Pickup only", "ingredients": "Ingredients", "allergens": "Allergens", "caffeineMg": "mg caffeine"}, "cart": {"notAvailableForDelivery": "Not available for delivery", "deliveryWarningTitle": "Delivery Restriction", "deliveryWarningMessage": "Some items in your cart are not available for delivery. You can still order them for pickup or table service.", "deliveryValidationMessage": "Some items in your cart are not available for delivery. Please remove them or choose pickup/table service.", "availableOffers": "Available Offers", "apply": "Apply", "applied": "Applied", "totalSavings": "You saved"}, "packages": {"packageDeal": "Package Deal", "save": "Save", "moreItems": "more", "youSave": "You save", "viewDetails": "View Details", "addPackage": "Add Package", "customizable": "Customizable", "items": "items", "packagePrice": "Package Price", "individualPrice": "Individual Price", "includedItems": "Included Items", "quantity": "Quantity", "optional": "Optional", "substitute": "Substitute with", "original": "Original", "packageQuantity": "Package Quantity", "totalPrice": "Total Price", "addToCart": "Add to Cart", "packageDeals": "Package Deals", "specialOffers": "Special Package Offers", "addedToCart": "Package added to cart!", "loadingPackage": "Loading Package...", "loadingItems": "Loading package items...", "offers": {"notFound": "Offer not found", "notFoundDesc": "The selected offer is no longer available.", "alreadyApplied": "Offer already applied", "alreadyAppliedDesc": "This offer has already been applied to your cart.", "notApplicable": "Offer not applicable", "notApplicableDesc": "This offer cannot be applied to your current cart.", "noDiscountApplicable": "No discount applicable", "noDiscountApplicableDesc": "This offer does not provide any discount for your current cart.", "applied": "Offer applied!", "appliedDesc": "You saved SAR {{amount}} with \"{{offerName}}\"", "errorApplying": "Error applying offer", "errorApplyingDesc": "There was an error applying the offer. Please try again.", "removed": "Offer removed", "removedDesc": "\"{{offer<PERSON><PERSON>}}\" has been removed from your cart.", "packageItemsRequired": "Package items required", "packageItemsRequiredDesc": "Please add all required package items to your cart first.", "insufficientQuantity": "Insufficient quantity", "insufficientQuantityDesc": "You need more of the required package items in your cart."}}, "checkout": {"deliveryOptions": "Delivery Options", "tableNumber": "Table Number", "enterTableNumber": "Enter Table Number", "selectTableZone": "Select Table Zone", "selectTableNumber": "Select Table Number", "selectTable": "Select a table", "tableZone": "Table Zone", "noTableZones": "No table zones available", "noTablesAvailable": "No tables available", "noDeliveryZones": "No delivery zones available", "tablePlaceholder": "e.g., A12", "pickUp": "Pick Up", "delivery": "Delivery", "selectDeliveryZone": "Select Delivery Zone", "deliveryFee": "Delivery Fee", "deliveryAddress": "Delivery Address", "addressPlaceholder": "Enter your full address", "proceedToDelivery": "Proceed to Delivery Options", "proceedToPayment": "Proceed to Payment", "confirmOrder": "Confirm Order", "orderSummary": "Order Summary", "deliveryType": "Delivery Type", "paymentMethod": "Payment Method", "cashOnDelivery": "Cash on Delivery/Pickup", "deliverySummary": "Delivery Summary", "deliveryZone": "Delivery Zone", "pickUpFromCounter": "Pick up from counter"}, "admin": {"dashboard": "Dashboard", "menuItems": "Menu Items", "categories": "Categories", "inventory": "Inventory", "deliveryZones": {"title": "Delivery Zones", "description": "Manage your delivery zones and pickup locations", "tabs": {"all": "All Zones", "pickUp": "Pick Up", "delivery": "Delivery", "inHouseTables": "In-House Tables"}}, "paymentSettings": "Payment Settings", "reviewsSection": "Reviews", "reviewsDescription": "Manage and view customer reviews", "totalReviews": "Total Reviews", "approvedReviews": "Approved Reviews", "pendingReviews": "Pending Reviews", "averageRating": "Average Rating", "searchReviews": "Search reviews by customer, order, or comment...", "allReviews": "All Reviews", "approved": "Approved", "pending": "Pending", "anonymousCustomer": "Anonymous Customer", "approve": "Approve", "delete": "Delete", "noReviewsFound": "No Reviews Found", "noReviewsMatchFilter": "No reviews match your current filter criteria.", "noReviewsYet": "No customer reviews have been submitted yet.", "reviewApproved": "Review Approved", "reviewApprovedDescription": "The review has been approved successfully.", "reviewDeleted": "Review Deleted", "reviewDeletedDescription": "The review has been deleted successfully.", "confirmDeleteReview": "Are you sure you want to delete this review? This action cannot be undone.", "reviewApproveError": "Failed to approve review. Please try again.", "reviewDeleteError": "Failed to delete review. Please try again.", "error": "Error", "qrGeneratorMenu": "QR Generator", "qrGeneratorDescription": "Generate QR codes for your cafe menu and tables", "qrGeneratorComingSoon": "QR code generator coming soon", "generateQR": "Generate QR", "viewMenuItems": "View Menu Items", "manageCategories": "Manage Categories", "qrGenerator": {"generateNew": "Generate New QR Code", "codeType": "QR Code Type", "menuItem": "<PERSON><PERSON>", "tableNumber": "Table Number", "specialOffer": "Special Offer", "size": "Size (px)", "contentUrl": "Content/URL", "urlPlaceholder": "Enter URL or content for QR code", "foregroundColor": "Foreground Color", "backgroundColor": "Background Color", "generateButton": "Generate QR Code", "preview": "Preview", "download": "Download", "print": "Print", "saveStyle": "Save Style", "menuRedirectNote": "QR code will redirect customers to your menu page", "deleteStyle": "Delete QR Style", "deleteConfirmMessage": "Are you sure you want to delete the QR style \"{{styleName}}\"? This action cannot be undone.", "delete": "Delete", "cancel": "Cancel", "deleting": "Deleting...", "saveQRStyle": "Save QR Style", "styleNameLabel": "Style Name", "styleNamePlaceholder": "Enter a name for this QR style", "descriptionLabel": "Description (Optional)", "descriptionPlaceholder": "Describe this QR style", "saveStyleButton": "Save Style", "recentCodes": "Recently Generated QR Codes", "table": "Table", "specialMenu": "Special Menu", "breakfastMenu": "Breakfast Menu", "weekendOffers": "Weekend Offers", "generatedOn": "Generated on"}, "manageCafe": "Manage Cafe", "totalOrders": "Total Orders", "totalRevenue": "Total Revenue", "totalCustomers": "Total Customers", "recentOrders": "Recent Orders", "topProducts": "Top Products", "orderStatusSection": "Order Status", "pendingReviewsCount": "Pending Reviews", "noDataMessage": "No data available yet", "viewAll": "View All", "welcomeMessage": "Welcome back! Here's what's happening with your cafe today.", "activeMenuItems": "Active Menu Items", "customerReviews": "Customer Reviews", "quickActions": "Quick Actions", "addItemMenu": "Add <PERSON>u <PERSON>em", "newOffer": "<PERSON> Offer", "syncInventory": "Sync Inventory", "viewReviews": "View Reviews", "orderId": "Order ID", "customer": "Customer", "items": "Items", "itemsCount": "items", "itemCount": "item", "total": "Total", "statusColumn": "Status", "actionColumn": "Action", "view": "View", "menuItemsDescription": "Manage your cafe's menu items", "manageMenuItems": "Manage your restaurant menu items", "addNewItem": "Add New Item", "searchMenuItems": "Search menu items...", "allCategories": "All Categories", "categoriesDescription": "Manage and organize your menu categories", "addCategory": "Add Category", "addNewCategory": "Add New Category", "clickToAddCategory": "Click to add a new menu category", "categoryStatistics": "Category Statistics", "totalCategories": "Total Categories", "activeCategories": "Active Categories", "featuredCategories": "Featured Categories", "availableHours": "Available: {{hours}}", "categoryName": "Category Name", "categoryNamePlaceholder": "e.g., Hot Beverages", "categoryNameArabic": "Category Name (Arabic)", "categoryNameArabicPlaceholder": "مثال: المشروبات الساخنة", "categoryDescription": "Category Description", "categoryDescriptionPlaceholder": "Describe this category...", "categoryDescriptionArabic": "Category Description (Arabic)", "categoryDescriptionArabicPlaceholder": "وصف هذه الفئة بالعربية...", "icon": "Icon", "availableFrom": "Available From", "availableTo": "Available To", "isActive": "Active", "isVisible": "Visible", "isFeatured": "Featured", "save": "Save", "editButton": "Edit", "cancelButton": "Cancel", "editCategory": "Edit Category", "deleteCategoryConfirmation": "Delete Category Confirmation", "deleteCategoryConfirmationMessage": "Are you sure you want to delete this category? This action cannot be undone.", "noCategories": "No categories found. Add your first category to get started!", "categoryCreated": "Category Created", "categoryCreatedDescription": "{{name}} has been created successfully.", "categoryUpdated": "Category Updated", "categoryUpdatedDescription": "{{name}} has been updated successfully.", "categoryDeleted": "Category Deleted", "categoryDeletedDescription": "{{name}} has been deleted successfully.", "errorFetchingCategories": "Error Fetching Categories", "errorSavingCategory": "Error Saving Category", "errorDeletingCategory": "Error Deleting Category", "iconOptions": {"hotBeverage": "Hot Beverage", "burger": "Burger", "pizza": "Pizza", "dessert": "Dessert", "utensils": "Utensils", "drinks": "Drinks", "iceCream": "Ice Cream", "fish": "Fish", "chicken": "Chicken", "preview": "Preview"}, "itemsCategories": {"hotDrinks": "Hot Drinks", "coldDrinks": "Cold Drinks", "desserts": "Desserts", "hotBeverages": "Hot Beverages", "mainCourse": "Main Course"}, "statusAll": "Status: All", "statusActive": "Active", "statusInactive": "Inactive", "inStock": "In Stock", "lowStock": "Low Stock", "editItem": "<PERSON>em", "previous": "Previous", "next": "Next", "itemsMenu": {"cappuccino": "Cappuccino", "cappuccinoDesc": "Rich espresso topped with creamy milk foam", "icedCaramelMacchiato": "Iced <PERSON><PERSON>", "icedCaramelMacchiatoDesc": "Espresso with vanilla and caramel", "chocolateCake": "Chocolate Cake", "chocolateCakeDesc": "Rich chocolate cake with fresh berries"}, "orderStatusTypes": {"completed": "Completed", "processing": "Processing", "new": "New"}, "stats": {"today": "Today's Sales", "thisWeek": "This Week", "thisMonth": "This Month", "thisYear": "This Year"}, "navigation": {"manageCafe": "Manage Cafe", "dashboard": "Dashboard", "menuItems": "Menu Items", "categories": "Categories", "offersAndDiscounts": "Offers & Discounts", "inventory": "Inventory", "deliveryZonesNav": "Delivery Zones", "paymentSettings": "Payment Settings", "reviews": "Reviews", "qrGenerator": "QR Generator"}, "addMenuItem": {"title": "Add New Menu Item", "description": "Description", "cancel": "Cancel", "itemName": "Item Name", "itemNamePlaceholder": "e.g., Cappuccino", "itemNameArabic": "<PERSON><PERSON> (Arabic)", "itemNameArabicPlaceholder": "e.g., كابتشينو", "category": "Category", "price": "Price", "prepTime": "Preparation Time (mins)", "initialStock": "Initial Stock", "descriptionPlaceholder": "Describe your menu item...", "descriptionArabic": "Description (Arabic)", "descriptionArabicPlaceholder": "وصف العنصر بالعربية...", "nutritionalInfo": "Nutritional Information", "nutritionalInfoDescription": "Provide nutritional details to help customers make informed choices", "caffeine": "Caffeine Content", "caffeinePlaceholder": "95mg, Low caffeine, Caffeine-free", "caffeineHint": "Enter caffeine amount (e.g., \"95mg\", \"Low caffeine\", \"Caffeine-free\") or leave empty", "caffeineArabic": "Caffeine Content (Arabic)", "caffeineArabicPlaceholder": "95 ملغ، كافيين منخفض، خالي من الكافيين", "caffeineArabicHint": "أدخل كمية الكافيين بالعربية أو اتركه فارغاً", "ingredients": "Ingredients", "ingredientsPlaceholder": "Espresso, steamed milk, milk foam", "ingredientsHint": "List main ingredients separated by commas", "ingredientsArabic": "Ingredients (Arabic)", "ingredientsArabicPlaceholder": "إسبريسو، حليب مبخر، رغوة الحليب", "ingredientsArabicHint": "اذكر المكونات الرئيسية مفصولة بفواصل", "allergens": "Allergens", "allergensPlaceholder": "Milk, nuts, gluten", "allergensHint": "List potential allergens separated by commas", "allergensArabic": "<PERSON><PERSON><PERSON> (Arabic)", "allergensArabicPlaceholder": "حليب، مكسرات، جلوتين", "allergensArabicHint": "اذكر مسببات الحساسية المحتملة مفصولة بفواصل", "itemImage": "Item Image", "dragAndDrop": "Drag and drop your image here, or", "browseFiles": "Browse Files", "maxFileSize": "Maximum file size: 5MB (JPG, PNG)", "featuredItem": "Featured Item", "availableForDelivery": "Available for Delivery", "saveAsDraft": "Save as Draft", "publishItem": "Publish Item", "snacks": "Snacks", "selectCategory": "Select a category", "options": "Options", "or": "or", "pasteImageUrl": "Paste image URL here", "saving": "Saving..."}, "outOfStock": "Out of Stock", "deleteItem": "Delete Item", "noMenuItems": "No menu items found. Add your first item!", "loadMore": "Load More", "editMenuItem": {"title": "<PERSON> <PERSON>", "description": "Update your menu item details", "cancel": "Cancel", "itemName": "Item Name", "category": "Category", "price": "Price", "prepTime": "Preparation Time (mins)", "stock": "Stock Quantity", "itemDescription": "Description", "nutritionalInfo": "Nutritional Information", "nutritionalInfoDescription": "Provide nutritional details to help customers make informed choices", "caffeine": "Caffeine Content", "caffeinePlaceholder": "95mg, Low caffeine, Caffeine-free", "caffeineHint": "Enter caffeine amount (e.g., \"95mg\", \"Low caffeine\", \"Caffeine-free\") or leave empty", "ingredients": "Ingredients", "ingredientsPlaceholder": "Espresso, steamed milk, milk foam", "ingredientsHint": "List main ingredients separated by commas", "allergens": "Allergens", "allergensPlaceholder": "Milk, nuts, gluten", "allergensHint": "List potential allergens separated by commas", "itemImage": "Item Image", "imageURL": "Image URL", "status": "Status", "active": "Active", "featured": "Featured Item", "deliveryAvailable": "Available for Delivery", "saveChanges": "Save Changes"}, "deliveryZoneTypes": {"pickUp": "Pick Up", "delivery": "Delivery", "inHouseTables": "In-House Tables"}, "zoneName": "Zone Name", "zoneNamePlaceholder": "Enter zone name", "zoneType": "Zone Type", "zoneDetails": "Details", "deliveryZoneStatus": "Status", "deliveryZoneActions": "Actions", "deliveryZoneActive": "Active", "deliveryZoneInactive": "Inactive", "deliveryZoneEdit": "Edit", "deliveryZoneDelete": "Delete", "confirmDelete": "Confirm Delete", "deleteZoneConfirmation": "Are you sure you want to delete this zone? This action cannot be undone.", "noZonesMessage": "No delivery zones available yet. Add your first zone below.", "addZone": "Add Zone", "updateZone": "Update Zone", "deliveryZoneDescription": "Description", "descriptionPlaceholder": "Enter description (optional)", "deliverySettings": "Delivery Settings", "deliveryFee": "Delivery Fee", "minOrder": "Min Order Amount", "radius": "Radius (km)", "estimatedTime": "Est. Time (min)", "tableSettings": "Table Settings", "tableNumberPlaceholder": "Enter table number", "addTable": "Add", "tables": "Tables", "noTablesAdded": "No tables added yet", "tableNumberExists": "Table number already exists", "nameRequired": "Name is required", "tableNumbersRequired": "At least one table number is required", "errorSavingZone": "Error saving delivery zone", "orders": "Orders", "manageOrders": "View and manage all customer orders", "filterByStatus": "Filter by Status", "allOrders": "All Orders", "searchOrders": "Search Orders", "searchPlaceholder": "Search by order ID, items, or address...", "date": "Date", "payment": "Payment", "edit": "Edit", "noOrders": "No Orders Found", "noSearchResults": "No orders match your search criteria.", "noOrdersYet": "No orders have been placed yet.", "deliveryInfo": "Delivery Information", "updateStatus": "Update Status", "editOrder": "Edit Order", "cannotEditOrder": "Cannot Edit Order", "orderItems": "Order Items", "addMenuItemButton": "Add <PERSON>u <PERSON>em", "selectMenuItem": "Select a menu item to add", "deliveryInformation": "Delivery Information", "saveChanges": "Save Changes", "offers": {"offersTitle": "Offers & Discounts", "offersDescription": "Manage and create offers and discounts for your menu items", "createOffer": "Create Offer", "editOffer": "Edit Offer", "updateOffer": "Update Offer", "searchOffers": "Search offers...", "noOffers": "No offers created yet. Create your first offer!", "noSearchResults": "No offers found matching your search.", "tabs": {"all": "All", "active": "Active", "expired": "Expired", "inactive": "Inactive", "packages": "Packages"}, "status": {"active": "Active", "inactive": "Inactive", "expired": "Expired"}, "basicInfo": "Basic Information", "offerName": "Offer Name", "offerNamePlaceholder": "e.g., Weekend Special", "offerNameArabic": "Offer Name (Arabic)", "offerNameArabicPlaceholder": "e.g., عرض نهاية الأسبوع", "description": "Description", "descriptionPlaceholder": "Describe your offer...", "descriptionArabic": "Description (Arabic)", "descriptionArabicPlaceholder": "وصف العرض...", "discountConfig": "Discount Configuration", "offerType": "Offer Type", "discountType": "Apply To", "discountPercentage": "Discount Percentage", "discountAmount": "Discount Amount (SAR)", "discountValue": "Discount Value", "packagePrice": "Package Price (SAR)", "allowCustomization": "Allow Item Substitutions", "types": {"percentage": "Percentage Discount", "fixedAmount": "Fixed Amount Discount", "freeDelivery": "Free Delivery", "packageDeal": "Package Deal"}, "discountTypes": {"orderTotal": "Entire Order", "category": "Specific Categories", "menuItem": "Specific Items", "deliveryFee": "Delivery Fee"}, "validityPeriod": "Validity Period", "startDate": "Start Date", "endDate": "End Date", "isActive": "Active", "usageLimits": "Usage Limits", "totalUsageLimit": "Total Usage Limit", "perUserLimit": "Per User Limit", "unlimited": "Unlimited", "conditions": "Conditions", "minOrderAmount": "Minimum Order Amount (SAR)", "maxDiscountAmount": "Maximum Discount Cap (SAR)", "loyaltyPointsRequired": "Loyalty Points Required", "firstOrderOnly": "First Order Only (New Customers)", "applicableCategories": "Applicable Categories", "applicableItems": "Applicable Items", "discount": "Discount", "usage": "Usage", "expires": "Expires", "freeDelivery": "Free Delivery", "packageDeal": "Package Deal", "offerCreatedSuccess": "Offer created successfully.", "offerUpdatedSuccess": "Offer updated successfully.", "offerDeletedSuccess": "Offer deleted successfully.", "createOfferError": "Failed to create offer. Please try again.", "updateOfferError": "Failed to update offer. Please try again.", "deleteOfferError": "Failed to delete offer. Please try again."}}, "customer": {"memberSince": "Member since {{date}}", "editProfile": "Edit Profile", "loyaltyPoints": "Loyalty Points", "totalOrders": "Total Orders", "giftCards": {"title": "Gift Cards", "subtitle": "Purchase new gift cards or manage your existing ones", "purchaseNew": "Purchase New Gift Card", "classicDesign": "Classic Design", "festiveDesign": "Festive Design", "selectAmount": "Select Amount", "customAmount": "Custom Amount", "purchaseButton": "Purchase Gift Card", "yourGiftCards": "Your Gift Cards", "cardEndingIn": "Card ending in", "balance": "Balance", "viewDetails": "View Details", "share": "Share", "shareSuccess": "Gift card shared successfully!", "fromFriend": "A friend", "shareGreeting": "🎉 Check out this amazing gift card! 🎁\n\nCard Number: {{cardNumber}}\nBalance: {{balance}}\nExpires: {{expiryDate}}", "shareGreetingExtended": "🎉 Surprise! {{senderName}} just sent you a gift card from {{cafeName}}!\n\n🎁 Gift Card Details:\n🔢 Card Number: {{cardNumber}}\n💰 Balance: {{balance}}\n📅 Expires: {{expiryDate}}\n\nTo redeem, just show this card number at Barcode Cafe or enter it in the app. Enjoy your treat! 😄", "shareGreetingEmojiSafe": "Surprise! {{senderName}} just sent you a gift card from {{cafeName}}!\n\n:: Gift Card Details ::\nCard Number: {{cardNumber}}\nBalance: {{balance}}\nExpires: {{expiryDate}}\n\nTo redeem, just show this card number at Barcode Cafe or enter it in the app. Enjoy your treat!", "shareCard": "Share Gift Card", "shareMessage": "Here's your gift card message:", "copyToClipboard": "Copy to clipboard", "copied": "Copied!", "shareWhatsApp": "Share via WhatsApp", "redeemGiftCard": "Redeem Gift Card", "enterGiftCardNumber": "Enter Gift Card Number", "enterPIN": "Enter PIN", "redeemButton": "Redeem Card", "noCardsYet": "You don't have any gift cards yet.", "expiresOn": "Expires on", "processing": "Processing...", "invalidAmount": "Please enter a valid amount", "purchaseSuccess": "Gift card purchased successfully!", "purchaseError": "There was an error purchasing your gift card. Please try again.", "invalidCode": "Please enter a valid gift card code", "cardNotFound": "Gift card not found", "alreadyRedeemed": "You have already redeemed this gift card", "redeemSuccess": "Gift card redeemed successfully!", "redeemError": "There was an error redeeming your gift card. Please try again."}, "recentOrders": "Recent Orders", "viewAll": "View All", "viewDetails": "View Details", "loyaltyProgramStatus": "Loyalty Program Status", "progressToGoldStatus": "Progress to Gold Status", "loyaltyProgressMessage": "Earn {{points}} more points to reach Gold Status and unlock exclusive rewards!", "loyalty": {"title": "Loyalty Program", "description": "Manage your loyalty points, view history, and redeem rewards", "scanReceipt": "Scan Receipt QR Code", "scanInstructions": "Position the QR code within the frame to scan", "pointsValue": "Points Value", "pointsToNextTier": "points to next tier", "redeemPoints": "Redeem Points", "pointsToRedeem": "Points to Redeem", "redemptionValue": "Redemption Value", "remainingPoints": "Remaining Points", "quickRedeem": "Quick Redeem", "scanQRCode": "Scan QR Code", "earnPointsOffline": "Earn Points from Offline Orders", "scanReceiptDescription": "Scan the QR code on your receipt from in-store purchases to earn loyalty points retroactively", "howItWorks": "How it works:", "step1": "• Make a purchase at our physical location", "step2": "• Find the QR code on your receipt", "step3": "• Scan it here to earn points", "step4": "• Points are added to your account instantly", "processing": "Processing...", "noTransactions": "No transactions yet", "noAchievements": "No achievements available", "unlocked": "Unlocked", "points": "points", "achievements": "Achievements", "highestTierAchieved": "Highest tier achieved!", "pointsEarned": "Loyalty Points Earned!", "pointsEarnedDescription": "You earned {{points}} points! Your new balance is {{newBalance}} points.", "tiers": {"bronze": "Bronze Member", "silver": "Silver Member", "gold": "Gold Member", "platinum": "Platinum Member"}}, "profile": "Customer Profile", "dashboard": {"welcome": "Welcome to your Dashboard"}, "items": {"caramelMacchiato": "<PERSON><PERSON>", "espressoDoubleShot": "Espresso Double Shot", "chocolateCroissant": "Chocolate Croissant", "orderPlaced": "Order Placed", "more": "more"}, "profileEdit": {"title": "Edit Profile", "personalInfo": "Personal Information", "firstName": "First Name", "lastName": "Last Name", "email": "Email Address", "phone": "Phone Number", "streetAddress": "Street Address", "city": "City", "state": "State", "postalCode": "Postal Code", "country": "Country", "profilePicture": "Change Photo", "maxFileSize": "Maximum file size: 2MB", "preferences": "Preferences", "emailNotifs": "Email Notifications", "emailNotifsDesc": "Receive order updates and promotions", "smsNotifs": "SMS Notifications", "smsNotifsDesc": "Get text messages for order status", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "enterStreetAddress": "Enter your street address", "enterCity": "Enter city", "enterState": "Enter state", "enterPostalCode": "Enter postal code", "enterCountry": "Enter country", "cancel": "Cancel", "saveChanges": "Save Changes", "emailNotEditable": "Email address cannot be changed", "saving": "Saving..."}, "emptyStates": {"orders": {"title": "No Orders Yet", "message": "You haven't placed any orders yet. Check out our menu to discover our delicious offerings!", "action": "<PERSON><PERSON><PERSON> Menu"}, "giftCards": {"title": "No Gift Cards", "message": "You don't have any gift cards yet. Purchase a gift card to enjoy later or share with friends and family.", "action": "Get Gift Card"}, "addresses": {"title": "No Saved Addresses", "message": "You don't have any saved addresses. Add your delivery addresses for faster checkout.", "action": "Add Address"}, "reviews": {"title": "No Reviews", "message": "You haven't written any reviews yet. Share your experience with our products!", "action": "Write Review"}, "recentOrders": {"title": "No Recent Orders", "message": "You haven't placed any orders recently. Check out our menu to order something delicious!", "action": "<PERSON><PERSON><PERSON> Menu"}, "settings": {"title": "Settings", "message": "Manage your account settings including notifications, language, and theme preferences.", "action": "Update Settings"}}}, "forgotPassword": {"title": "Forgot Password", "subtitle": "Enter your email address and we'll send you a password reset link", "emailLabel": "Email address", "emailPlaceholder": "Enter your email", "submitButton": "Send Reset Link", "backToLogin": "Back to login", "successMessage": "If your email exists in our system, you will receive a password reset link shortly.", "errorMessage": "Please enter a valid email address."}, "addresses": {"name": "Name", "namePlaceholder": "e.g., Home, Work", "addressLine1": "Address Line 1", "addressLine1Placeholder": "Street address, P.O. box", "addressLine2": "Address Line 2", "addressLine2Placeholder": "Apartment, suite, unit, building, floor, etc.", "city": "City", "cityPlaceholder": "Enter city", "state": "State/Province", "statePlaceholder": "Enter state or province", "postalCode": "Postal Code", "postalCodePlaceholder": "Enter postal code", "country": "Country", "countryPlaceholder": "Enter country", "makeDefault": "Make this my default address", "default": "<PERSON><PERSON><PERSON>", "addAddress": "Add Address", "editAddress": "Edit Address", "saveChanges": "Save Changes", "deleteConfirmTitle": "Delete Address", "deleteConfirmMessage": "Are you sure you want to delete this address? This action cannot be undone.", "delete": "Delete"}, "reviews": {"rating": "Rating", "comment": "Comment", "commentPlaceholder": "Tell us about your experience... What did you like or dislike?", "submitReview": "Submit Review", "updateReview": "Update Review", "writeReview": "Write a Review", "edit": "Edit", "delete": "Delete", "deleteConfirmTitle": "Delete Review", "deleteConfirmMessage": "Are you sure you want to delete this review? This action cannot be undone.", "pastOrders": "Past Orders You Can Review", "reviewOrder": "Review {{orderName}}", "editReview": "Edit Review", "alreadyReviewed": "Already Reviewed", "reviewThisOrder": "Write a Review", "title": "Reviews", "titlePlaceholder": "Brief summary of your experience", "content": "Description", "contentPlaceholder": "Tell us more about your experience in detail", "addReview": "Add Review", "saveChanges": "Save Changes", "rateYourOrder": "Rate Your Order", "orderNumber": "Order Number", "yourOrder": "Your Order", "howWasYourExperience": "How was your experience?", "stars": "stars", "additionalComments": "Additional Comments", "skipReview": "Skip Review", "submitting": "Submitting...", "thankYouForReview": "Thank you for your review!", "reviewSubmitted": "Your review has been submitted successfully."}, "orders": {"status": {"orderPlaced": "Order placed", "preparing": "Preparing", "readyForPickup": "Ready for pickup", "outForDelivery": "Out for delivery", "delivered": "Delivered", "cancelled": "Cancelled"}, "items": "Items", "viewDetails": "View Details", "specialInstructions": "Special Instructions", "orderDetails": "Order Details", "orderNumber": "Order Number", "paymentMethod": "Payment Method", "paymentMethods": {"creditCard": "Credit Card", "debitCard": "Debit Card", "cash": "Cash", "giftCard": "Gift Card", "loyaltyPoints": "Loyalty Points"}, "summary": "Order Summary", "subtotal": "Subtotal", "tax": "Tax", "total": "Total", "printReceipt": "Print Receipt", "cancelOrder": "Cancel Order", "cancellation": {"title": "Cancel Order", "reason": "Cancellation Reason", "selectReason": "Select a reason", "notes": "Additional Notes", "notesPlaceholder": "Any additional information...", "refundInfo": "Refund Information", "refundAmount": "Refund Amount", "refundMethod": "Refund Method", "warning": "This action cannot be undone. The order will be permanently cancelled.", "confirmCancel": "Confirm Cancellation", "cancelling": "Cancelling...", "success": "Order Cancelled", "successMessage": "The order has been successfully cancelled", "error": "Error", "reasonRequired": "Please select a cancellation reason", "reasons": {"changed_mind": "Changed Mind", "wrong_order": "Wrong Order", "delivery_delay": "Delivery Delay", "payment_issue": "Payment Issue", "out_of_stock": "Out of Stock", "kitchen_issue": "Kitchen Issue", "delivery_unavailable": "Delivery Unavailable", "customer_request": "Customer Request", "payment_failed": "Payment Failed", "duplicate_order": "Duplicate Order", "other": "Other"}, "refundMethods": {"cash_refund": "Cash Refund", "card_refund": "Card Refund", "gift_card_credit": "Gift Card Credit", "points_credit": "Points Credit", "manual_refund": "Manual Refund"}}, "notFound": {"title": "Order Not Found", "message": "The order you're looking for doesn't exist or you don't have permission to view it."}, "backToOrders": "Back to Orders"}, "receipt": {"title": "Receipt Preview", "orderInfo": "Order Information", "receiptNo": "Receipt No", "orderNo": "Order No", "date": "Date", "cashier": "Cashier", "paymentMethod": "Payment Method", "customerInfo": "Customer Information", "customer": "Customer", "address": "Address", "table": "Table", "pickup": "Pickup Order", "items": "Order Items", "notes": "Notes", "subtotal": "Subtotal", "deliveryFee": "Delivery Fee", "tax": "Tax", "total": "TOTAL", "thankYou": "Thank you for your visit!", "enjoyMeal": "Enjoy your meal and have a great day!", "followUs": "Follow us on instagram @barcode_ksa", "powered": "Powered by Barcode Cafe System", "downloadPDF": "Download PDF"}}