/**
 * Seed script to initialize the loyalty system with default rules and achievements
 * Run this script after deploying the loyalty system to set up the initial configuration
 */

import { 
  initializeDefaultLoyaltyRules, 
  initializeDefaultAchievements,
  getLoyaltyRules,
  getActiveAchievements
} from '@/lib/firebase/firestore';

export async function seedLoyaltySystem(): Promise<void> {
  console.log('🌱 Starting loyalty system seed...');

  try {
    // Initialize default loyalty rules
    console.log('📋 Initializing loyalty rules...');
    const rules = await initializeDefaultLoyaltyRules();
    console.log('✅ Loyalty rules initialized:', rules.id);

    // Initialize default achievements
    console.log('🏆 Initializing achievements...');
    const achievements = await initializeDefaultAchievements();
    console.log(`✅ ${achievements.length} achievements initialized`);

    // Verify the setup
    console.log('🔍 Verifying setup...');
    const verifyRules = await getLoyaltyRules();
    const verifyAchievements = await getActiveAchievements();
    
    console.log(`✅ Verification complete:`);
    console.log(`   - Loyalty rules: ${verifyRules ? 'Found' : 'Missing'}`);
    console.log(`   - Active achievements: ${verifyAchievements.length}`);

    console.log('🎉 Loyalty system seed completed successfully!');

  } catch (error) {
    console.error('❌ Error seeding loyalty system:', error);
    throw error;
  }
}

// Export individual functions for selective seeding
export async function seedLoyaltyRules(): Promise<void> {
  console.log('📋 Seeding loyalty rules only...');
  const rules = await initializeDefaultLoyaltyRules();
  console.log('✅ Loyalty rules seeded:', rules.id);
}

export async function seedAchievements(): Promise<void> {
  console.log('🏆 Seeding achievements only...');
  const achievements = await initializeDefaultAchievements();
  console.log(`✅ ${achievements.length} achievements seeded`);
}

// Run the seed if this file is executed directly
if (require.main === module) {
  seedLoyaltySystem()
    .then(() => {
      console.log('Seed completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Seed failed:', error);
      process.exit(1);
    });
}
