'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { usePhoneAuth } from '@/contexts/PhoneAuthContext';
import { useLocale } from '@/contexts/LocaleContext';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Phone, MessageSquare, User } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import LanguageSwitcher from '@/components/ui/LanguageSwitcher';
import DarkModeToggle from '@/components/ui/DarkModeToggle';
import Logo from '@/components/ui/Logo';
import InternationalPhoneInput from '@/components/ui/InternationalPhoneInput';
import { CountryCode, DEFAULT_COUNTRY } from '@/lib/country-codes';

export default function PhoneAuthPage() {
  const { t, dir, isClient } = useLocale();
  const router = useRouter();
  const searchParams = useSearchParams();
  const {
    user,
    loading,
    isAdmin,
    adminCheckComplete,
    verificationStep,
    phoneNumber,
    isCodeSent,
    isVerifying,
    sendVerificationCode,
    verifyCode,
    resendCode,
    resetVerification
  } = usePhoneAuth();

  const [phone, setPhone] = useState('');
  const [code, setCode] = useState('');
  const [displayName, setDisplayName] = useState('');
  const [selectedCountry, setSelectedCountry] = useState<CountryCode>(DEFAULT_COUNTRY);
  const [isLoading, setIsLoading] = useState(false);
  const [verificationTimeout, setVerificationTimeout] = useState<NodeJS.Timeout | null>(null);

  // Redirect if already authenticated or verification complete
  useEffect(() => {
    if (!loading && user && verificationStep === 'complete') {
      const redirectPath = searchParams.get('from');

      // Add a small delay to ensure all state updates are complete
      const redirectTimer = setTimeout(() => {
        if (redirectPath) {
          console.log('Redirecting to:', redirectPath);
          router.push(redirectPath);
        } else if (isAdmin) {
          console.log('Redirecting to admin dashboard');
          router.push('/admin/dashboard');
        } else {
          console.log('Redirecting to customer dashboard');
          router.push('/customer/dashboard');
        }
      }, 100);

      return () => clearTimeout(redirectTimer);
    }
  }, [user, loading, isAdmin, verificationStep, router, searchParams]);

  // Additional effect to handle immediate redirect after verification
  useEffect(() => {
    console.log('PhoneAuth Page: Verification step:', verificationStep);
    console.log('PhoneAuth Page: User:', user ? user.uid : 'null');
    console.log('PhoneAuth Page: IsAdmin:', isAdmin);
    console.log('PhoneAuth Page: Loading:', loading);

    // Don't redirect if still loading or no user
    if (loading || !user) return;

    if (verificationStep === 'complete' && user) {
      const redirectPath = searchParams.get('from');

      console.log('PhoneAuth Page: Triggering redirect...');
      console.log('PhoneAuth Page: Redirect path:', redirectPath);

      // Add a small delay to ensure all state is properly set
      const redirectTimer = setTimeout(() => {
        console.log('PhoneAuth Page: About to redirect with state:', {
          redirectPath,
          isAdmin,
          user: user?.uid,
          adminCheckComplete
        });

        if (redirectPath && redirectPath !== '/verify-email') {
          console.log('PhoneAuth Page: Redirecting to custom path:', redirectPath);
          router.replace(redirectPath);
        } else if (isAdmin) {
          console.log('PhoneAuth Page: Redirecting to admin dashboard');
          router.replace('/admin/dashboard');
        } else {
          console.log('PhoneAuth Page: Redirecting to customer dashboard');
          router.replace('/customer/dashboard');
        }
      }, 500); // Small delay to ensure state is stable

      return () => clearTimeout(redirectTimer);
    }
  }, [verificationStep, user, isAdmin, router, searchParams, loading]);

  // Add a timeout for verification process to prevent infinite loading
  useEffect(() => {
    if (verificationStep === 'complete' && user && loading) {
      const timeout = setTimeout(() => {
        console.warn('PhoneAuth Page: Verification timeout, forcing redirect');
        if (isAdmin) {
          router.replace('/admin/dashboard');
        } else {
          router.replace('/customer/dashboard');
        }
      }, 5000); // 5 second timeout

      setVerificationTimeout(timeout);
      return () => clearTimeout(timeout);
    }

    // Clear timeout if verification step changes
    if (verificationTimeout) {
      clearTimeout(verificationTimeout);
      setVerificationTimeout(null);
    }
  }, [verificationStep, user, loading, isAdmin, router, verificationTimeout]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (verificationTimeout) {
        clearTimeout(verificationTimeout);
      }
    };
  }, [verificationTimeout]);

  const handleSendCode = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!phone.trim()) {
      toast({
        variant: "destructive",
        title: isClient ? t('phoneAuth.error') : "Error",
        description: isClient ? t('phoneAuth.phoneRequired') : "Please enter your phone number",
      });
      return;
    }

    setIsLoading(true);
    try {
      await sendVerificationCode(phone, selectedCountry);
    } catch (error) {
      console.error('Error sending code:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePhoneChange = (value: string, country: CountryCode) => {
    setPhone(value);
    setSelectedCountry(country);
  };

  const handleVerifyCode = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!code.trim()) {
      toast({
        variant: "destructive",
        title: isClient ? t('phoneAuth.error') : "Error",
        description: isClient ? t('phoneAuth.codeRequired') : "Please enter the verification code",
      });
      return;
    }

    setIsLoading(true);
    try {
      await verifyCode(code, displayName || undefined);
    } catch (error) {
      console.error('Error verifying code:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendCode = async () => {
    setIsLoading(true);
    try {
      await resendCode();
      setCode(''); // Clear the code input
    } catch (error) {
      console.error('Error resending code:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    if (verificationStep === 'code') {
      resetVerification();
      setPhone('');
      setCode('');
      setDisplayName('');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  // Dynamic RTL-aware classes
  const rtlAwareClasses = {
    textAlign: dir === 'rtl' ? 'text-right' : 'text-left',
    labelClass: `block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 ${dir === 'rtl' ? 'text-right' : 'text-left'}`,
  };

  return (
    <div
      className="min-h-screen bg-[#F9FDFC] dark:bg-[#171c23] flex flex-col"
      dir={isClient ? dir : "ltr"}
    >
      {/* Header */}
      <header className="py-6 px-8">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <Logo linkTo="/" />
          <div className="flex items-center gap-3">
            <DarkModeToggle />
            <LanguageSwitcher />
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 flex items-center justify-center px-4 py-12">
        <div className="w-full max-w-md bg-white dark:bg-[#1d2127] rounded-2xl shadow-lg p-8">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-100 mb-2">
              {isClient ? t('phoneAuth.title') : "Welcome to BarcodeCafe"}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {isClient ? t('phoneAuth.subtitle') : "Sign in with your phone number"}
            </p>
          </div>

          {/* Step indicator */}
          <div className="flex items-center justify-center mb-6">
            <div className="flex items-center space-x-4">
              <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                verificationStep === 'phone' ? 'bg-[#83EAED] text-white' :
                ['code', 'profile', 'complete'].includes(verificationStep) ? 'bg-green-500 text-white' :
                'bg-gray-200 dark:bg-gray-700 text-gray-500'
              }`}>
                <Phone className="h-4 w-4" />
              </div>
              <div className={`w-8 h-0.5 ${
                ['code', 'profile', 'complete'].includes(verificationStep) ? 'bg-green-500' : 'bg-gray-200 dark:bg-gray-700'
              }`}></div>
              <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                verificationStep === 'code' ? 'bg-[#83EAED] text-white' :
                ['profile', 'complete'].includes(verificationStep) ? 'bg-green-500 text-white' :
                'bg-gray-200 dark:bg-gray-700 text-gray-500'
              }`}>
                <MessageSquare className="h-4 w-4" />
              </div>
              <div className={`w-8 h-0.5 ${
                ['profile', 'complete'].includes(verificationStep) ? 'bg-green-500' : 'bg-gray-200 dark:bg-gray-700'
              }`}></div>
              <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
                verificationStep === 'profile' ? 'bg-[#83EAED] text-white' :
                verificationStep === 'complete' ? 'bg-green-500 text-white' :
                'bg-gray-200 dark:bg-gray-700 text-gray-500'
              }`}>
                <User className="h-4 w-4" />
              </div>
            </div>
          </div>

          {/* Step title and description */}
          <div className="text-center mb-6">
            <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">
              {verificationStep === 'phone' && (isClient ? t('phoneAuth.phoneStep.title') : "Enter Phone Number")}
              {verificationStep === 'code' && (isClient ? t('phoneAuth.codeStep.title') : "Verify Code")}
              {verificationStep === 'profile' && (isClient ? t('phoneAuth.profileStep.title') : "Complete Profile")}
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {verificationStep === 'phone' && (isClient ? t('phoneAuth.phoneStep.description') : "We'll send you a verification code via SMS")}
              {verificationStep === 'code' && (isClient ? t('phoneAuth.codeStep.description', { phoneNumber }) : `Enter the 6-digit code sent to ${phoneNumber}`)}
              {verificationStep === 'profile' && (isClient ? t('phoneAuth.profileStep.description') : "Please provide your name to complete registration")}
            </p>
          </div>

          {/* Phone Number Step */}
          {verificationStep === 'phone' && (
            <form onSubmit={handleSendCode} className="space-y-4">
              <div className="space-y-1">
                <label className={rtlAwareClasses.labelClass}>
                  {isClient ? t('phoneAuth.phoneLabel') : "Phone Number"}
                </label>
                <InternationalPhoneInput
                  value={phone}
                  onChange={handlePhoneChange}
                  selectedCountry={selectedCountry}
                  onCountryChange={setSelectedCountry}
                  disabled={isLoading || isVerifying}
                  className="w-full"
                />
              </div>

              {/* reCAPTCHA container */}
              <div id="recaptcha-container" className="flex justify-center"></div>

              <button
                type="submit"
                disabled={isLoading || isVerifying}
                className="w-full bg-[#83EAED] dark:bg-[#5DBDC0] text-white py-3 rounded-lg hover:bg-[#6ED3D6] dark:hover:bg-[#4A9EA0] transition-colors font-medium flex justify-center items-center"
              >
                {(isLoading || isVerifying) && (
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                )}
                {isClient ? t('phoneAuth.sendCodeButton') : "Send Verification Code"}
              </button>
            </form>
          )}

          {/* Verification Code Step */}
          {verificationStep === 'code' && (
            <form onSubmit={handleVerifyCode} className="space-y-4">
              <div className="space-y-1">
                <label className={rtlAwareClasses.labelClass} htmlFor="code">
                  {isClient ? t('phoneAuth.codeLabel') : "Verification Code"}
                </label>
                <input
                  id="code"
                  name="code"
                  type="text"
                  value={code}
                  onChange={(e) => setCode(e.target.value)}
                  className={`w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 dark:bg-[#242832] dark:text-gray-100 focus:ring-2 focus:ring-[#66B8B1] dark:focus:ring-[#5DBDC0] focus:border-transparent text-center text-lg tracking-widest`}
                  placeholder={isClient ? t('phoneAuth.codePlaceholder') : "Enter 6-digit code"}
                  maxLength={6}
                  disabled={isLoading || isVerifying}
                />
              </div>

              <div className="space-y-1">
                <label className={rtlAwareClasses.labelClass} htmlFor="displayName">
                  {isClient ? t('phoneAuth.nameLabel') : "Your Name (Optional)"}
                </label>
                <input
                  id="displayName"
                  name="displayName"
                  type="text"
                  value={displayName}
                  onChange={(e) => setDisplayName(e.target.value)}
                  className={`w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 dark:bg-[#242832] dark:text-gray-100 focus:ring-2 focus:ring-[#66B8B1] dark:focus:ring-[#5DBDC0] focus:border-transparent ${rtlAwareClasses.textAlign}`}
                  placeholder={isClient ? t('phoneAuth.namePlaceholder') : "Enter your full name"}
                  disabled={isLoading || isVerifying}
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {isClient ? t('phoneAuth.nameHelp') : "This will be used for your orders and profile"}
                </p>
              </div>

              <div className="flex gap-2">
                <button
                  type="button"
                  onClick={handleBack}
                  disabled={isLoading || isVerifying}
                  className="flex-1 px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-[#242832] transition-colors font-medium"
                >
                  {isClient ? t('phoneAuth.backButton') : "Back"}
                </button>
                <button
                  type="submit"
                  disabled={isLoading || isVerifying}
                  className="flex-1 bg-[#83EAED] dark:bg-[#5DBDC0] text-white py-3 rounded-lg hover:bg-[#6ED3D6] dark:hover:bg-[#4A9EA0] transition-colors font-medium flex justify-center items-center"
                >
                  {(isLoading || isVerifying) && (
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  )}
                  {isClient ? t('phoneAuth.verifyButton') : "Verify"}
                </button>
              </div>

              <div className="text-center">
                <button
                  type="button"
                  onClick={handleResendCode}
                  disabled={isLoading || isVerifying}
                  className="text-sm text-[#66B8B1] dark:text-[#5DBDC0] hover:text-[#4d8a85] dark:hover:text-[#4A9EA0] transition-colors"
                >
                  {isClient ? t('phoneAuth.resendCode') : "Didn't receive the code? Resend"}
                </button>
              </div>
            </form>
          )}

          {/* Success/Redirect State */}
          {verificationStep === 'complete' && (
            <div className="text-center py-8">
              <div className="bg-green-100 dark:bg-green-900/20 w-16 h-16 flex items-center justify-center rounded-full mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600 dark:text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">
                {isClient ? t('phoneAuth.verificationComplete') : "Verification Complete!"}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                {isClient ? t('phoneAuth.redirecting') : "Redirecting you to the dashboard..."}
              </p>
              <div className="flex justify-center mb-4">
                <svg className="animate-spin h-6 w-6 text-[#66B8B1]" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>

              {/* Fallback manual redirect buttons */}
              <div className="space-y-2">
                <button
                  onClick={() => {
                    const redirectPath = searchParams.get('from');
                    if (redirectPath) {
                      router.push(redirectPath);
                    } else if (isAdmin) {
                      router.push('/admin/dashboard');
                    } else {
                      router.push('/customer/dashboard');
                    }
                  }}
                  className="w-full bg-[#83EAED] dark:bg-[#5DBDC0] text-white py-2 px-4 rounded-lg hover:bg-[#6ED3D6] dark:hover:bg-[#4A9EA0] transition-colors text-sm"
                >
                  {isClient ? t('phoneAuth.continueManually') : "Continue to Dashboard"}
                </button>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {isClient ? t('phoneAuth.redirectIssue') : "If redirect doesn't work automatically, click the button above"}
                </p>
              </div>
            </div>
          )}

          {/* Terms and Privacy */}
          {verificationStep !== 'complete' && (
            <div className="mt-8 text-center">
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {isClient ? t('phoneAuth.termsText') : "By continuing, you agree to our Terms of Service and Privacy Policy"}
              </p>
            </div>
          )}
        </div>
      </main>

      {/* Footer */}
      <footer className="py-6 text-center text-gray-500 dark:text-gray-400 text-sm">
        <p>
          {isClient ? t('common.footer') : "© 2025 Barcode Cafe. All rights reserved."}
        </p>
      </footer>
    </div>
  );
}
