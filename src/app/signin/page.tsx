"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { useEffect } from "react";

export default function SignInPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Redirect to phone authentication page
  useEffect(() => {
    const redirectPath = searchParams.get('from');
    const phoneAuthUrl = redirectPath
      ? `/phone-auth?from=${encodeURIComponent(redirectPath)}`
      : '/phone-auth';

    router.replace(phoneAuthUrl);
  }, [router, searchParams]);

  // Loading component while redirecting
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
        <p>Redirecting to phone authentication...</p>
      </div>
    </div>
  );
}