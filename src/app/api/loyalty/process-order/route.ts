import { NextRequest, NextResponse } from 'next/server';
import { processOrderLoyaltyPoints } from '@/lib/firebase/firestore';

// Check if we're in a build environment
const isBuildTime = process.env.NEXT_PUBLIC_SKIP_FIREBASE_ADMIN_INIT === 'true';

/**
 * POST /api/loyalty/process-order
 * Process order completion and award loyalty points
 */
export async function POST(request: NextRequest) {
  // Skip actual processing during build time
  if (isBuildTime) {
    console.log('Skipping loyalty order processing during build');
    return NextResponse.json({ 
      success: true, 
      message: 'Skipped during build time',
      data: { pointsEarned: 0 }
    });
  }

  try {
    const { orderId, userId, orderAmount } = await request.json();

    // Validate required fields
    if (!orderId || !userId || !orderAmount) {
      return NextResponse.json(
        {
          success: false,
          message: 'Missing required fields: orderId, userId, orderAmount'
        },
        { status: 400 }
      );
    }

    // Validate orderAmount is a positive number
    if (typeof orderAmount !== 'number' || orderAmount <= 0) {
      return NextResponse.json(
        {
          success: false,
          message: 'Order amount must be a positive number'
        },
        { status: 400 }
      );
    }

    console.log(`Processing loyalty points for order ${orderId}, user ${userId}, amount ${orderAmount}`);

    // Process loyalty points
    const result = await processOrderLoyaltyPoints(orderId, userId, orderAmount);

    console.log(`Awarded ${result.pointsEarned} points to user ${userId}`);

    return NextResponse.json({
      success: true,
      message: 'Loyalty points processed successfully',
      data: {
        pointsEarned: result.pointsEarned,
        transactionId: result.transaction.id,
        newBalance: result.updatedProfile.loyaltyPoints,
        newTier: result.updatedProfile.loyaltyTier,
        pointsToNextTier: result.updatedProfile.pointsToNextTier
      }
    });

  } catch (error) {
    console.error('Error processing loyalty points:', error);
    
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to process loyalty points',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
