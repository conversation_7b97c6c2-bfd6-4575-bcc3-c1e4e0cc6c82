import { NextRequest, NextResponse } from 'next/server';
import { 
  initializeDefaultLoyaltyRules, 
  initializeDefaultAchievements,
  getLoyaltyRules,
  getActiveAchievements
} from '@/lib/firebase/firestore';

// Check if we're in a build environment
const isBuildTime = process.env.NEXT_PUBLIC_SKIP_FIREBASE_ADMIN_INIT === 'true';

/**
 * POST /api/loyalty/initialize
 * Initialize the loyalty system with default rules and achievements
 */
export async function POST(request: NextRequest) {
  // Skip actual processing during build time
  if (isBuildTime) {
    console.log('Skipping loyalty initialization during build');
    return NextResponse.json({ 
      success: true, 
      message: 'Skipped during build time',
      data: { rulesId: 'build-mock', achievementsCount: 0 }
    });
  }

  try {
    console.log('🌱 Starting loyalty system initialization...');

    // Check if already initialized
    const existingRules = await getLoyaltyRules();
    const existingAchievements = await getActiveAchievements();

    if (existingRules && existingAchievements.length > 0) {
      return NextResponse.json({
        success: true,
        message: 'Loyalty system already initialized',
        data: {
          rulesId: existingRules.id,
          achievementsCount: existingAchievements.length
        }
      });
    }

    // Initialize loyalty rules
    console.log('📋 Initializing loyalty rules...');
    const rules = await initializeDefaultLoyaltyRules();

    // Initialize achievements
    console.log('🏆 Initializing achievements...');
    const achievements = await initializeDefaultAchievements();

    console.log('✅ Loyalty system initialization completed');

    return NextResponse.json({
      success: true,
      message: 'Loyalty system initialized successfully',
      data: {
        rulesId: rules.id,
        achievementsCount: achievements.length
      }
    });

  } catch (error) {
    console.error('❌ Error initializing loyalty system:', error);
    
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to initialize loyalty system',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/loyalty/initialize
 * Check loyalty system initialization status
 */
export async function GET(request: NextRequest) {
  // Skip actual processing during build time
  if (isBuildTime) {
    console.log('Skipping loyalty status check during build');
    return NextResponse.json({ 
      success: true, 
      initialized: false,
      message: 'Build time - status unknown'
    });
  }

  try {
    const [rules, achievements] = await Promise.all([
      getLoyaltyRules(),
      getActiveAchievements()
    ]);

    const initialized = rules !== null && achievements.length > 0;

    return NextResponse.json({
      success: true,
      initialized,
      data: {
        hasRules: rules !== null,
        rulesId: rules?.id || null,
        achievementsCount: achievements.length
      }
    });

  } catch (error) {
    console.error('Error checking loyalty system status:', error);
    
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to check loyalty system status',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
