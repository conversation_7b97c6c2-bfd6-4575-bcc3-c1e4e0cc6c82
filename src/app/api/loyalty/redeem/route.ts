import { NextRequest, NextResponse } from 'next/server';
import { redeemPointsFromUser, getLoyaltyRules } from '@/lib/firebase/firestore';

// Check if we're in a build environment
const isBuildTime = process.env.NEXT_PUBLIC_SKIP_FIREBASE_ADMIN_INIT === 'true';

/**
 * POST /api/loyalty/redeem
 * Redeem loyalty points for discounts
 */
export async function POST(request: NextRequest) {
  // Skip actual processing during build time
  if (isBuildTime) {
    console.log('Skipping loyalty redemption during build');
    return NextResponse.json({ 
      success: true, 
      message: 'Skipped during build time',
      data: { pointsRedeemed: 0, sarValue: 0 }
    });
  }

  try {
    const { userId, points, description, description_ar, orderId } = await request.json();

    // Validate required fields
    if (!userId || !points) {
      return NextResponse.json(
        {
          success: false,
          message: 'Missing required fields: userId, points'
        },
        { status: 400 }
      );
    }

    // Validate points is a positive number
    if (typeof points !== 'number' || points <= 0) {
      return NextResponse.json(
        {
          success: false,
          message: 'Points must be a positive number'
        },
        { status: 400 }
      );
    }

    // Get loyalty rules for validation
    const rules = await getLoyaltyRules();
    if (!rules) {
      return NextResponse.json(
        {
          success: false,
          message: 'Loyalty system not configured'
        },
        { status: 500 }
      );
    }

    // Validate minimum redemption
    if (points < rules.minRedemptionPoints) {
      return NextResponse.json(
        {
          success: false,
          message: `Minimum redemption is ${rules.minRedemptionPoints} points`
        },
        { status: 400 }
      );
    }

    console.log(`Processing points redemption for user ${userId}, points: ${points}`);

    // Calculate SAR value
    const sarValue = points / rules.pointsToSAR;

    // Process redemption
    const result = await redeemPointsFromUser(
      userId,
      points,
      description || `Redeemed ${points} points for ${sarValue.toFixed(2)} SAR discount`,
      description_ar || `استبدال ${points} نقطة مقابل خصم ${sarValue.toFixed(2)} ريال`,
      orderId
    );

    console.log(`Successfully redeemed ${points} points for user ${userId}`);

    return NextResponse.json({
      success: true,
      message: 'Points redeemed successfully',
      data: {
        pointsRedeemed: points,
        sarValue: sarValue,
        transactionId: result.transaction.id,
        newBalance: result.updatedProfile.loyaltyPoints,
        newTier: result.updatedProfile.loyaltyTier,
        pointsToNextTier: result.updatedProfile.pointsToNextTier
      }
    });

  } catch (error) {
    console.error('Error redeeming points:', error);
    
    // Handle specific error types
    if (error instanceof Error) {
      if (error.message === 'User profile not found') {
        return NextResponse.json(
          {
            success: false,
            message: 'User not found'
          },
          { status: 404 }
        );
      }
      
      if (error.message === 'Insufficient loyalty points') {
        return NextResponse.json(
          {
            success: false,
            message: 'Insufficient loyalty points for this redemption'
          },
          { status: 400 }
        );
      }
    }
    
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to redeem points',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
