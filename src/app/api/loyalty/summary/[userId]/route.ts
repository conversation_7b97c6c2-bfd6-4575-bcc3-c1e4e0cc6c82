import { NextRequest, NextResponse } from 'next/server';
import { getUserLoyaltySummary } from '@/lib/firebase/firestore';

// Check if we're in a build environment
const isBuildTime = process.env.NEXT_PUBLIC_SKIP_FIREBASE_ADMIN_INIT === 'true';

/**
 * GET /api/loyalty/summary/[userId]
 * Get user loyalty summary including points, tier, achievements, and recent transactions
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  // Skip actual processing during build time
  if (isBuildTime) {
    console.log('Skipping loyalty summary during build');
    return NextResponse.json({ 
      success: true, 
      message: 'Skipped during build time',
      data: null
    });
  }

  try {
    const { userId } = params;

    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          message: 'User ID is required'
        },
        { status: 400 }
      );
    }

    console.log(`Getting loyalty summary for user ${userId}`);

    // Get user loyalty summary
    const summary = await getUserLoyaltySummary(userId);

    return NextResponse.json({
      success: true,
      message: 'Loyalty summary retrieved successfully',
      data: {
        profile: {
          loyaltyPoints: summary.profile.loyaltyPoints,
          loyaltyTier: summary.profile.loyaltyTier,
          totalPointsEarned: summary.profile.totalPointsEarned,
          pointsToNextTier: summary.profile.pointsToNextTier,
          achievements: summary.profile.achievements,
          streakCount: summary.profile.streakCount
        },
        recentTransactions: summary.recentTransactions.map(t => ({
          id: t.id,
          type: t.type,
          source: t.source,
          points: t.points,
          description: t.description,
          description_ar: t.description_ar,
          createdAt: t.createdAt,
          expiryDate: t.expiryDate
        })),
        achievements: {
          completed: summary.completedAchievements.map(a => ({
            id: a.id,
            name: a.name,
            name_ar: a.name_ar,
            description: a.description,
            description_ar: a.description_ar,
            icon: a.icon,
            pointsReward: a.pointsReward,
            type: a.type
          })),
          available: summary.availableAchievements.map(a => ({
            id: a.id,
            name: a.name,
            name_ar: a.name_ar,
            description: a.description,
            description_ar: a.description_ar,
            icon: a.icon,
            pointsReward: a.pointsReward,
            type: a.type,
            conditions: a.conditions
          }))
        },
        pointsExpiringIn30Days: summary.pointsExpiringIn30Days
      }
    });

  } catch (error) {
    console.error('Error getting loyalty summary:', error);
    
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to get loyalty summary',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
