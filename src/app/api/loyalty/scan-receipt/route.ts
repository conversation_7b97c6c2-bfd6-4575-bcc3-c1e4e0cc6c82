import { NextRequest, NextResponse } from 'next/server';
import { processReceiptQRScan } from '@/lib/firebase/firestore';

// Check if we're in a build environment
const isBuildTime = process.env.NEXT_PUBLIC_SKIP_FIREBASE_ADMIN_INIT === 'true';

/**
 * POST /api/loyalty/scan-receipt
 * Process receipt QR scan and award loyalty points
 */
export async function POST(request: NextRequest) {
  // Skip actual processing during build time
  if (isBuildTime) {
    console.log('Skipping receipt scan processing during build');
    return NextResponse.json({ 
      success: true, 
      message: 'Skipped during build time',
      data: { pointsEarned: 0 }
    });
  }

  try {
    const { qrCode, userId } = await request.json();

    // Validate required fields
    if (!qrCode || !userId) {
      return NextResponse.json(
        {
          success: false,
          message: 'Missing required fields: qrCode, userId'
        },
        { status: 400 }
      );
    }

    console.log(`Processing receipt QR scan for user ${userId}, QR code: ${qrCode}`);

    // Process receipt scan
    const result = await processReceiptQRScan(qrCode, userId);

    console.log(`Awarded ${result.pointsEarned} points to user ${userId} from receipt scan`);

    return NextResponse.json({
      success: true,
      message: 'Receipt scanned successfully',
      data: {
        pointsEarned: result.pointsEarned,
        receiptNumber: result.receipt.receiptNumber,
        receiptTotal: result.receipt.total,
        scanId: result.scan.id
      }
    });

  } catch (error) {
    console.error('Error processing receipt scan:', error);
    
    let statusCode = 500;
    let errorMessage = 'Failed to process receipt scan';

    // Handle specific error cases
    if (error instanceof Error) {
      if (error.message.includes('Receipt not found')) {
        statusCode = 404;
        errorMessage = 'Receipt not found';
      } else if (error.message.includes('already been scanned')) {
        statusCode = 409;
        errorMessage = 'Receipt has already been scanned';
      } else if (error.message.includes('not enabled')) {
        statusCode = 403;
        errorMessage = 'Receipt scanning is not enabled';
      }
    }
    
    return NextResponse.json(
      {
        success: false,
        message: errorMessage,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: statusCode }
    );
  }
}
