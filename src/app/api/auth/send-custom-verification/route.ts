import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/firebase/admin';
import nodemailer from 'nodemailer';
import fs from 'fs';
import path from 'path';

// Check if we're in a build environment
const isBuildTime = process.env.NEXT_PUBLIC_SKIP_FIREBASE_ADMIN_INIT === 'true';

/**
 * Sends a custom verification email using a template and nodemailer
 * 
 * In a production environment, you would:
 * 1. Set up a proper email service (SendGrid, Mailgun, etc.)
 * 2. Store email templates in a proper template management system
 * 3. Add more error handling and retry logic
 */
export async function POST(request: NextRequest) {
  // Skip actual processing during build time
  if (isBuildTime) {
    console.log('Skipping send-custom-verification API route execution during build');
    return NextResponse.json({ success: true, message: 'Verification email sent' });
  }
  try {
    const { uid, email, displayName } = await request.json();
    
    if (!uid || !email) {
      return NextResponse.json(
        { 
          success: false,
          message: 'User ID and email are required'
        }, 
        { status: 400 }
      );
    }
    
    // Generate a verification link using Firebase Admin SDK
    // Note: In production, you should use a proper email service like SendGrid or Mailgun
    try {
      // Get the current domain from the request headers
      const host = request.headers.get('host');
      const protocol = request.headers.get('x-forwarded-proto') || 'http';
      const baseUrl = host ? `${protocol}://${host}` : (process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000');

      console.log('Custom verification email - using base URL:', baseUrl);

      // Generate the verification link
      const actionCodeSettings = {
        url: `${baseUrl}/auth/action`,
        handleCodeInApp: true,
        // iOS app settings (if you have an iOS app)
        iOS: {
          bundleId: 'com.barcodecafe.app'
        },
        // Android app settings (if you have an Android app)
        android: {
          packageName: 'com.barcodecafe.app',
          installApp: true,
          minimumVersion: '12'
        }
      };
      
      const link = await auth.generateEmailVerificationLink(
        email, 
        actionCodeSettings
      );
      
      // Read the email template
      const templatePath = path.join(process.cwd(), 'src/lib/email-templates/verification-email.html');
      let emailTemplate = '';
      
      try {
        emailTemplate = fs.readFileSync(templatePath, 'utf8');
      } catch (err) {
        console.error('Error reading email template:', err);
        return NextResponse.json(
          { 
            success: false, 
            message: 'Error reading email template' 
          },
          { status: 500 }
        );
      }
      
      // Replace placeholders in the template
      emailTemplate = emailTemplate.replace(/{{VERIFICATION_LINK}}/g, link);
      
      if (displayName) {
        emailTemplate = emailTemplate.replace(/{{NAME}}/g, displayName);
      }
      
      // Create a test SMTP transport (you would use a real service in production)
      // For demonstration purposes only
      // In production, use a proper email service (SendGrid, Mailgun, etc.)
      const transporter = nodemailer.createTransport({
        host: process.env.EMAIL_HOST || 'smtp.example.com',
        port: parseInt(process.env.EMAIL_PORT || '587'),
        secure: process.env.EMAIL_SECURE === 'true',
        auth: {
          user: process.env.EMAIL_USER || '<EMAIL>',
          pass: process.env.EMAIL_PASSWORD || 'password',
        },
      });
      
      // Send the email
      await transporter.sendMail({
        from: process.env.EMAIL_FROM || '"BarcodeCafe" <<EMAIL>>',
        to: email,
        subject: 'Verify your email address - BarcodeCafe',
        html: emailTemplate,
      });
      
      return NextResponse.json({ 
        success: true,
        message: 'Verification email sent'
      });
    } catch (error) {
      console.error('Error sending verification email:', error);
      
      return NextResponse.json(
        { 
          success: false,
          message: 'Error sending verification email'
        }, 
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Server error in send-custom-verification:', error);
    
    return NextResponse.json(
      { 
        success: false,
        message: 'Server error'
      }, 
      { status: 500 }
    );
  }
} 