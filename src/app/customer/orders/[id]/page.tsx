"use client";

import { useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation";
import { useLocale } from "@/contexts/LocaleContext";
import { useAuth } from "@/hooks/useAuth";
import DashboardLayout from "@/components/customer-dashboard/DashboardLayout";
import { getOrder } from "@/lib/firebase/firestore";
import { Order, OrderStatus, PaymentMethod } from "@/types/models";
import { formatDate } from "@/lib/utils";
import ReceiptModal from "@/components/receipt/ReceiptModal";
import CancelOrderModal from "@/components/orders/CancelOrderModal";
import RiyalSymbol from "@/components/ui/RiyalSymbol";

export default function OrderDetailsPage() {
  // Use the useParams hook to get the id parameter
  const params = useParams();
  const orderId = params?.id as string;
  const router = useRouter();
  const { t, isClient, locale } = useLocale();
  const { user, loading, isEmailVerified } = useAuth();
  const [order, setOrder] = useState<Order | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isReceiptModalOpen, setIsReceiptModalOpen] = useState(false);
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);

  // Open receipt modal
  const handlePrintReceipt = () => {
    setIsReceiptModalOpen(true);
  };

  // Open cancel modal
  const handleCancelOrder = () => {
    setIsCancelModalOpen(true);
  };

  // Handle order cancellation
  const handleOrderCancelled = (cancelledOrder: Order) => {
    setOrder(cancelledOrder);
  };
  
  // Load order details
  useEffect(() => {
    const fetchOrderDetails = async () => {
      if (user?.uid && orderId) {
        setIsLoading(true);
        try {
          const orderData = await getOrder(orderId);
          
          // Verify that the order belongs to the current user
          if (orderData && orderData.userId === user.uid) {
            setOrder(orderData);
          } else {
            // Order not found or doesn't belong to this user
            router.replace("/customer/orders");
          }
        } catch (error) {
          console.error("Error fetching order details:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };
    
    if (user) {
      fetchOrderDetails();
    }
  }, [user, orderId, router]);
  
  // Redirect to signin if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push("/signin");
    } else if (user && !isEmailVerified) {
      // Redirect to verification page if email is not verified
      router.push("/verify-email");
    }
  }, [loading, user, router]);

  const getPaymentMethodLabel = (method: PaymentMethod): string => {
    switch (method) {
      case PaymentMethod.CREDIT_CARD:
        return t("orders.paymentMethods.creditCard");
      case PaymentMethod.DEBIT_CARD:
        return t("orders.paymentMethods.debitCard");
      case PaymentMethod.CASH:
        return t("orders.paymentMethods.cash");
      case PaymentMethod.GIFT_CARD:
        return t("orders.paymentMethods.giftCard");
      case PaymentMethod.LOYALTY_POINTS:
        return t("orders.paymentMethods.loyaltyPoints");
      default:
        return method;
    }
  };

  const getOrderStatusLabel = (status: OrderStatus): string => {
    switch (status) {
      case OrderStatus.ORDER_PLACED:
        return t("orders.status.orderPlaced");
      case OrderStatus.PREPARING:
        return t("orders.status.preparing");
      case OrderStatus.READY_FOR_PICKUP:
        return t("orders.status.readyForPickup");
      case OrderStatus.OUT_FOR_DELIVERY:
        return t("orders.status.outForDelivery");
      case OrderStatus.DELIVERED:
        return t("orders.status.delivered");
      case OrderStatus.CANCELLED:
        return t("orders.status.cancelled");
      default:
        return status;
    }
  };

  const getStatusBadgeClass = (status: OrderStatus): string => {
    switch (status) {
      case OrderStatus.ORDER_PLACED:
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-500";
      case OrderStatus.PREPARING:
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-500";
      case OrderStatus.READY_FOR_PICKUP:
        return "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-500";
      case OrderStatus.OUT_FOR_DELIVERY:
        return "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-500";
      case OrderStatus.DELIVERED:
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-500";
      case OrderStatus.CANCELLED:
        return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-500";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-500";
    }
  };

  if (!isClient) {
    return null;
  }

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  return (
    <DashboardLayout>
      <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm">
        <div className="p-6 border-b border-gray-100 dark:border-gray-800 flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold dark:text-gray-100">
              {t("orders.orderDetails")}
            </h1>
            {order && (
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {t("orders.orderNumber")}: #{order.id.slice(-8).toUpperCase()}
              </p>
            )}
          </div>
          <button
            onClick={() => router.back()}
            className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
          >
            <i className="fa-solid fa-arrow-left mr-2"></i>
            {t("common.back")}
          </button>
        </div>
        
        {isLoading ? (
          <div className="flex justify-center p-16">
            <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
          </div>
        ) : order ? (
          <div className="p-6">
            {/* Order Status and Date */}
            <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-8">
              <div className="mb-4 md:mb-0">
                <span className={`text-sm px-3 py-1 rounded-full ${getStatusBadgeClass(order.status)}`}>
                  {getOrderStatusLabel(order.status)}
                </span>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                  {order.createdAt instanceof Date 
                    ? formatDate(order.createdAt, true) 
                    : typeof order.createdAt === 'string' 
                      ? formatDate(new Date(order.createdAt), true)
                      : ''}
                </p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {t("orders.paymentMethod")}
                </p>
                <p className="font-medium dark:text-gray-100">
                  {getPaymentMethodLabel(order.paymentMethod)}
                </p>
              </div>
            </div>

            {/* Order Items */}
            <div className="bg-gray-50 dark:bg-[#242832] rounded-lg p-6 mb-6">
              <h2 className="text-lg font-medium mb-4 dark:text-gray-100">
                {t("orders.items")}
              </h2>
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {order.items.map((item, index) => (
                  <div key={index} className="py-4 flex justify-between">
                    <div>
                      <p className="font-medium dark:text-gray-100">
                        {item.quantity}x {item.name}
                      </p>
                      {item.options && item.options.length > 0 && (
                        <ul className="mt-1 space-y-1">
                          {item.options.map((option, optIndex) => (
                            <li key={optIndex} className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                              <span className="w-4 h-0 border-t border-gray-300 dark:border-gray-600 mr-2"></span>
                              {option.name}: {option.value}
                              {option.priceAdjustment > 0 && (
                                <span className="ml-1 flex items-center gap-1">
                                  (+<RiyalSymbol size={10} className="text-gray-500 dark:text-gray-400" />
                                  {option.priceAdjustment.toFixed(2)})
                                </span>
                              )}
                            </li>
                          ))}
                        </ul>
                      )}
                    </div>
                    <p className="font-medium dark:text-gray-100 flex items-center gap-1">
                      <RiyalSymbol size={14} className="text-gray-800 dark:text-gray-100" />
                      {(item.price * item.quantity).toFixed(2)}
                    </p>
                  </div>
                ))}
              </div>
            </div>

            {/* Special Instructions */}
            {order.specialInstructions && (
              <div className="bg-gray-50 dark:bg-[#242832] rounded-lg p-6 mb-6">
                <h2 className="text-lg font-medium mb-2 dark:text-gray-100">
                  {t("orders.specialInstructions")}
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  {order.specialInstructions}
                </p>
              </div>
            )}

            {/* Order Summary */}
            <div className="bg-gray-50 dark:bg-[#242832] rounded-lg p-6">
              <h2 className="text-lg font-medium mb-4 dark:text-gray-100">
                {t("orders.summary")}
              </h2>
              
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-500 dark:text-gray-400">{t("orders.subtotal")}</span>
                  <span className="dark:text-gray-100 flex items-center gap-1">
                    <RiyalSymbol size={12} className="text-gray-800 dark:text-gray-100" />
                    {(order.total * 0.85).toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500 dark:text-gray-400">{t("orders.tax")} (15%)</span>
                  <span className="dark:text-gray-100 flex items-center gap-1">
                    <RiyalSymbol size={12} className="text-gray-800 dark:text-gray-100" />
                    {(order.total * 0.15).toFixed(2)}
                  </span>
                </div>
                <div className="border-t border-gray-200 dark:border-gray-700 pt-2 mt-2">
                  <div className="flex justify-between font-medium">
                    <span className="dark:text-gray-100">{t("orders.total")}</span>
                    <span className="dark:text-gray-100 flex items-center gap-1">
                      <RiyalSymbol size={14} className="text-gray-800 dark:text-gray-100" />
                      {order.total.toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Order Actions */}
            <div className="mt-8 flex justify-end gap-3">
              {order.status === OrderStatus.ORDER_PLACED && (
                <button
                  onClick={handleCancelOrder}
                  className="bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-500 px-4 py-2 rounded-lg hover:bg-red-200 dark:hover:bg-red-900/50 transition-colors"
                >
                  <i className="fa-solid fa-ban mr-2"></i>
                  {t("orders.cancelOrder")}
                </button>
              )}
              <button
                className="bg-[#83EAED] dark:bg-[#5DBDC0] text-white px-6 py-2 rounded-lg hover:bg-[#83EAED]/90 dark:hover:bg-[#4A9EA0] transition-colors"
                onClick={handlePrintReceipt}
              >
                <i className="fa-solid fa-print mr-2"></i>
                {t("orders.printReceipt")}
              </button>
            </div>

          </div>
        ) : (
          <div className="p-16 text-center">
            <i className="fa-solid fa-circle-exclamation text-4xl text-gray-400 dark:text-gray-600 mb-4"></i>
            <h2 className="text-xl font-medium mb-2 dark:text-gray-100">
              {t("orders.notFound.title")}
            </h2>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              {t("orders.notFound.message")}
            </p>
            <button 
              className="bg-[#83EAED] dark:bg-[#5DBDC0] text-white px-6 py-2 rounded-lg hover:bg-[#83EAED]/90 dark:hover:bg-[#4A9EA0]"
              onClick={() => router.push("/customer/orders")}
            >
              {t("orders.backToOrders")}
            </button>
          </div>
        )}
      </div>

      {/* Receipt Modal */}
      <ReceiptModal
        order={order}
        isOpen={isReceiptModalOpen}
        onClose={() => setIsReceiptModalOpen(false)}
      />

      {/* Cancel Order Modal */}
      <CancelOrderModal
        order={order}
        isOpen={isCancelModalOpen}
        onClose={() => setIsCancelModalOpen(false)}
        onOrderCancelled={handleOrderCancelled}
        userType="customer"
      />
    </DashboardLayout>
  );
}
