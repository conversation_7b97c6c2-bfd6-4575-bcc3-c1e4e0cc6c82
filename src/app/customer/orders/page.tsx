"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useLocale } from "@/contexts/LocaleContext";
import { useAuth } from "@/hooks/useAuth";
import DashboardLayout from "@/components/customer-dashboard/DashboardLayout";
import EmptyState from "@/components/ui/EmptyState";
import { getUserOrders } from "@/lib/firebase/firestore";
import { Order, OrderStatus, PaymentMethod } from "@/types/models";
import { formatDate } from "@/lib/utils";
import RiyalSymbol from "@/components/ui/RiyalSymbol";

export default function OrderHistoryPage() {
  const router = useRouter();
  const { t, isClient } = useLocale();
  const { user, loading, isEmailVerified } = useAuth();
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  // Load orders
  useEffect(() => {
    const fetchOrders = async () => {
      if (user?.uid) {
        setIsLoading(true);
        try {
          const userOrders = await getUserOrders(user.uid);
          setOrders(userOrders);
        } catch (error) {
          console.error("Error fetching orders:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };
    
    if (user) {
      fetchOrders();
    }
  }, [user]);
  
  // Redirect to signin if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push("/signin");
    } else if (user && !isEmailVerified) {
      // Redirect to verification page if email is not verified
      router.push("/verify-email");
    }
  }, [loading, user, router]);

  const handleBrowseMenu = () => {
    router.push("/menu");
  };

  if (!isClient) {
    return null;
  }

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  return (
    <DashboardLayout>
      <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm">
        <div className="p-6 border-b border-gray-100 dark:border-gray-800">
          <h1 className="text-2xl font-bold dark:text-gray-100">
            {t("nav.orderHistory")}
          </h1>
        </div>
        
        {isLoading ? (
          <div className="flex justify-center p-16">
            <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
          </div>
        ) : orders.length > 0 ? (
          <div className="divide-y divide-gray-100 dark:divide-gray-800">
            {orders.map(order => (
              <div key={order.id} className="p-6 hover:bg-gray-50 dark:hover:bg-[#242832]">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-medium dark:text-gray-100">
                        {order.items.length > 0 
                          ? `${order.items[0].name}${order.items.length > 1 ? ` + ${order.items.length - 1} ${t("customer.items.more")}` : ''}`
                          : t("customer.items.orderPlaced")}
                      </h3>
                      <span className={`text-xs px-2 py-1 rounded-full ${getStatusBadgeClass(order.status)}`}>
                        {getOrderStatusLabel(order.status, t)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {order.createdAt instanceof Date 
                        ? formatDate(order.createdAt) 
                        : typeof order.createdAt === 'string' 
                          ? formatDate(new Date(order.createdAt))
                          : ''}
                    </p>
                  </div>
                  <div className="flex flex-col md:items-end">
                    <span className="font-medium dark:text-gray-100 flex items-center gap-1">
                      <RiyalSymbol size={14} className="text-gray-800 dark:text-gray-100" />
                      {order.total.toFixed(2)}
                    </span>
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {getPaymentMethodLabel(order.paymentMethod, t)}
                    </span>
                  </div>
                </div>
                
                <div className="mt-4">
                  <h4 className="text-sm font-medium mb-2 dark:text-gray-300">{t("orders.items")}</h4>
                  <ul className="space-y-1">
                    {order.items.map((item, index) => (
                      <li key={index} className="text-sm text-gray-600 dark:text-gray-400 flex justify-between">
                        <span>
                          {item.quantity}x {item.name}
                          {item.options && item.options.length > 0 && (
                            <span className="text-gray-500 dark:text-gray-500 text-xs">
                              {" "}({item.options.map(opt => opt.value).join(", ")})
                            </span>
                          )}
                        </span>
                        <span className="flex items-center gap-1">
                          <RiyalSymbol size={12} className="text-gray-600 dark:text-gray-400" />
                          {(item.price * item.quantity).toFixed(2)}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                {order.specialInstructions && (
                  <div className="mt-3 p-3 bg-gray-50 dark:bg-[#242832] rounded-lg">
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      <span className="font-medium dark:text-gray-300">{t("orders.specialInstructions")}: </span>
                      {order.specialInstructions}
                    </p>
                  </div>
                )}
                
                <div className="mt-4 flex justify-end">
                  <button 
                    className="text-[#56999B] dark:text-[#5DBDC0] text-sm hover:underline"
                    onClick={() => router.push(`/customer/orders/${order.id}`)}
                  >
                    {t("orders.viewDetails")}
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <EmptyState
            icon="fa-solid fa-receipt"
            title={isClient ? t("customer.emptyStates.orders.title") : "No Orders Yet"}
            message={isClient ? t("customer.emptyStates.orders.message") : "You haven't placed any orders yet. Check out our menu to discover our delicious offerings!"}
            actionLabel={isClient ? t("customer.emptyStates.orders.action") : "Browse Menu"}
            onAction={handleBrowseMenu}
            className="py-16"
          />
        )}
      </div>
    </DashboardLayout>
  );
}

// Helper functions for order status
function getOrderStatusLabel(status: OrderStatus, t: (key: string) => string): string {
  switch (status) {
    case OrderStatus.ORDER_PLACED:
      return t("orders.status.orderPlaced");
    case OrderStatus.PREPARING:
      return t("orders.status.preparing");
    case OrderStatus.READY_FOR_PICKUP:
      return t("orders.status.readyForPickup");
    case OrderStatus.OUT_FOR_DELIVERY:
      return t("orders.status.outForDelivery");
    case OrderStatus.DELIVERED:
      return t("orders.status.delivered");
    case OrderStatus.CANCELLED:
      return t("orders.status.cancelled");
    default:
      return status;
  }
}

// Helper function for payment method labels
function getPaymentMethodLabel(method: PaymentMethod, t: (key: string) => string): string {
  switch (method) {
    case PaymentMethod.CREDIT_CARD:
      return t("orders.paymentMethods.creditCard");
    case PaymentMethod.DEBIT_CARD:
      return t("orders.paymentMethods.debitCard");
    case PaymentMethod.CASH:
      return t("orders.paymentMethods.cash");
    case PaymentMethod.GIFT_CARD:
      return t("orders.paymentMethods.giftCard");
    case PaymentMethod.LOYALTY_POINTS:
      return t("orders.paymentMethods.loyaltyPoints");
    default:
      return method;
  }
}

function getStatusBadgeClass(status: OrderStatus): string {
  switch (status) {
    case OrderStatus.ORDER_PLACED:
      return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-500";
    case OrderStatus.PREPARING:
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-500";
    case OrderStatus.READY_FOR_PICKUP:
      return "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-500";
    case OrderStatus.OUT_FOR_DELIVERY:
      return "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-500";
    case OrderStatus.DELIVERED:
      return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-500";
    case OrderStatus.CANCELLED:
      return "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-500";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-500";
  }
} 