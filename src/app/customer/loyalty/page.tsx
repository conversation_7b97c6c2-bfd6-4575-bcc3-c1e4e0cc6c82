"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useLocale } from "@/contexts/LocaleContext";
import { useAuth } from "@/hooks/useAuth";
import DashboardLayout from "@/components/customer-dashboard/DashboardLayout";
import QRScanner from "@/components/loyalty/QRScanner";
import {
  getUserProfile,
  getLoyaltyRules,
  redeemPointsFromUser,
  getUserLoyaltyTransactions,
  getActiveAchievements,
  getUserLoyaltySummary,
  processReceiptQRScan
} from "@/lib/firebase/firestore";
import { 
  UserProfile, 
  LoyaltyRules, 
  LoyaltyTransaction, 
  Achievement,
  LoyaltyTier,
  LoyaltyTransactionType 
} from "@/types/models";
import { formatDate } from "@/lib/utils";
import RiyalSymbol from "@/components/ui/RiyalSymbol";
import { toast } from "@/hooks/use-toast";

export default function CustomerLoyaltyPage() {
  const router = useRouter();
  const { t, isClient, locale } = useLocale();
  const { user, loading } = useAuth();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loyaltyRules, setLoyaltyRules] = useState<LoyaltyRules | null>(null);
  const [transactions, setTransactions] = useState<LoyaltyTransaction[]>([]);
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRedeeming, setIsRedeeming] = useState(false);
  const [redemptionAmount, setRedemptionAmount] = useState<number>(0);
  const [activeTab, setActiveTab] = useState<'redeem' | 'history' | 'achievements' | 'scan'>('redeem');
  const [isQRScannerOpen, setIsQRScannerOpen] = useState(false);
  const [isProcessingReceipt, setIsProcessingReceipt] = useState(false);

  // Initialize loyalty system if needed
  const initializeLoyaltySystem = async () => {
    try {
      console.log("Initializing loyalty system...");
      const response = await fetch('/api/loyalty/initialize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.message || 'Failed to initialize loyalty system');
      }

      console.log("Loyalty system initialized:", result.data);
      return true;
    } catch (error) {
      console.error("Error initializing loyalty system:", error);
      return false;
    }
  };

  // Fetch user loyalty data
  useEffect(() => {
    const fetchLoyaltyData = async () => {
      if (user?.uid) {
        setIsLoading(true);
        try {
          // First, try to get the data
          let [profile, rules, userTransactions, allAchievements] = await Promise.all([
            getUserProfile(user.uid),
            getLoyaltyRules(),
            getUserLoyaltyTransactions(user.uid, 20),
            getActiveAchievements()
          ]);

          // Check if user profile has loyalty fields, if not, update it
          if (profile && (profile.loyaltyPoints === undefined || profile.loyaltyTier === undefined)) {
            console.log("User profile missing loyalty fields, updating...");
            try {
              const { updateUserProfile } = await import('@/lib/firebase/firestore');
              const updatedProfile = await updateUserProfile(user.uid, {
                loyaltyPoints: 0,
                loyaltyTier: 'bronze' as any,
                totalPointsEarned: 0,
                pointsToNextTier: 1000,
                achievements: [],
                streakCount: 0,
                favoriteItems: []
              });
              profile = updatedProfile;
              console.log("User profile updated with loyalty fields");
            } catch (error) {
              console.error("Error updating user profile with loyalty fields:", error);
            }
          }

          // If loyalty rules or achievements are missing, initialize the system
          if (!rules || allAchievements.length === 0) {
            console.log("Loyalty system not initialized, initializing...");
            const initialized = await initializeLoyaltySystem();

            if (initialized) {
              // Retry fetching the data
              [profile, rules, userTransactions, allAchievements] = await Promise.all([
                getUserProfile(user.uid),
                getLoyaltyRules(),
                getUserLoyaltyTransactions(user.uid, 20),
                getActiveAchievements()
              ]);
            }
          }

          setUserProfile(profile);
          setLoyaltyRules(rules);
          setTransactions(userTransactions);
          setAchievements(allAchievements);

          // Log the data for debugging
          console.log("Loyalty data loaded:", {
            profile: !!profile,
            rules: !!rules,
            transactions: userTransactions.length,
            achievements: allAchievements.length
          });

        } catch (error) {
          console.error("Error fetching loyalty data:", error);
          toast({
            title: "Error",
            description: "Failed to load loyalty data. Please try again.",
            variant: "destructive",
          });
        } finally {
          setIsLoading(false);
        }
      }
    };

    if (user) {
      fetchLoyaltyData();
    }
  }, [user]);

  // Redirect if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.replace("/phone-auth");
    }
  }, [loading, user, router]);

  // Calculate redemption values
  const calculateRedemptionValue = (points: number): number => {
    if (!loyaltyRules) return 0;
    return points / loyaltyRules.pointsToSAR;
  };

  // Handle points redemption
  const handleRedemption = async () => {
    if (!user?.uid || !userProfile || !loyaltyRules || redemptionAmount <= 0) return;

    // Validate minimum redemption
    if (redemptionAmount < loyaltyRules.minRedemptionPoints) {
      toast({
        title: "Invalid Amount",
        description: `Minimum redemption is ${loyaltyRules.minRedemptionPoints} points`,
        variant: "destructive",
      });
      return;
    }

    // Validate user has enough points
    if (redemptionAmount > userProfile.loyaltyPoints) {
      toast({
        title: "Insufficient Points",
        description: "You don't have enough points for this redemption",
        variant: "destructive",
      });
      return;
    }

    setIsRedeeming(true);
    try {
      const sarValue = calculateRedemptionValue(redemptionAmount);
      const result = await redeemPointsFromUser(
        user.uid,
        redemptionAmount,
        `Redeemed ${redemptionAmount} points for ${sarValue.toFixed(2)} SAR discount`,
        `استبدال ${redemptionAmount} نقطة مقابل خصم ${sarValue.toFixed(2)} ريال`
      );

      // Update local state
      setUserProfile(result.updatedProfile);
      setTransactions(prev => [result.transaction, ...prev]);
      setRedemptionAmount(0);

      toast({
        title: "Redemption Successful!",
        description: `You've redeemed ${redemptionAmount} points for ${sarValue.toFixed(2)} SAR discount`,
      });
    } catch (error) {
      console.error("Error redeeming points:", error);
      toast({
        title: "Redemption Failed",
        description: "Failed to redeem points. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsRedeeming(false);
    }
  };

  // Handle QR code scan
  const handleQRScan = async (qrData: string) => {
    if (!user?.uid) return;

    setIsProcessingReceipt(true);
    try {
      const result = await processReceiptQRScan(qrData, user.uid);

      // Update local state
      if (userProfile) {
        setUserProfile({
          ...userProfile,
          loyaltyPoints: userProfile.loyaltyPoints + result.pointsEarned
        });
      }

      // Refresh transactions
      const updatedTransactions = await getUserLoyaltyTransactions(user.uid, 20);
      setTransactions(updatedTransactions);

      toast({
        title: "Receipt Scanned Successfully!",
        description: `You earned ${result.pointsEarned} points from receipt #${result.receipt.receiptNumber}`,
      });
    } catch (error) {
      console.error("Error processing receipt:", error);
      toast({
        title: "Scan Failed",
        description: error instanceof Error ? error.message : "Failed to process receipt. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessingReceipt(false);
    }
  };

  // Get tier display info
  const getTierInfo = (tier: LoyaltyTier) => {
    switch (tier) {
      case LoyaltyTier.BRONZE:
        return { name: 'Bronze', color: 'text-amber-600', icon: '🥉' };
      case LoyaltyTier.SILVER:
        return { name: 'Silver', color: 'text-gray-500', icon: '🥈' };
      case LoyaltyTier.GOLD:
        return { name: 'Gold', color: 'text-yellow-500', icon: '🥇' };
      case LoyaltyTier.PLATINUM:
        return { name: 'Platinum', color: 'text-purple-500', icon: '💎' };
      default:
        return { name: 'Bronze', color: 'text-amber-600', icon: '🥉' };
    }
  };

  if (loading || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  if (!user || !userProfile || !loyaltyRules) {
    return (
      <DashboardLayout>
        <div className="text-center py-16">
          <i className="fa-solid fa-exclamation-triangle text-4xl text-gray-300 dark:text-gray-600 mb-4"></i>
          <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2">
            Unable to load loyalty information
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            {!user ? "Please sign in to access your loyalty account" :
             !userProfile ? "User profile not found" :
             !loyaltyRules ? "Loyalty system is being set up. Please try again in a moment." :
             "Unknown error occurred"}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="bg-[#66B8B1] hover:bg-[#5a9fa0] text-white py-2 px-4 rounded-lg"
          >
            Try Again
          </button>
        </div>
      </DashboardLayout>
    );
  }

  const tierInfo = getTierInfo(userProfile.loyaltyTier);
  const maxRedemption = Math.floor(userProfile.loyaltyPoints / loyaltyRules.minRedemptionPoints) * loyaltyRules.minRedemptionPoints;

  return (
    <DashboardLayout>
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 dark:text-white mb-2">
          {isClient ? t("customer.loyalty.title") : "Loyalty Program"}
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          {isClient ? t("customer.loyalty.description") : "Manage your loyalty points, view history, and redeem rewards"}
        </p>
      </div>

      {/* Loyalty Status Card */}
      <div className="bg-gradient-to-r from-[#83EAED] to-[#66B8B1] rounded-xl p-6 text-white mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold mb-2">
              {isClient ? t("customer.loyalty.points") : "Points"} {userProfile.loyaltyPoints.toLocaleString()}
            </h2>
            <div className="flex items-center gap-2">
              <span className="text-lg">{tierInfo.icon}</span>
              <span className="text-lg font-semibold">
                {isClient ? t(`customer.loyalty.tiers.${userProfile.loyaltyTier}`) : `${tierInfo.name} Member`}
              </span>
            </div>
            <p className="text-sm opacity-90 mt-1">
              {userProfile.pointsToNextTier > 0
                ? `${userProfile.pointsToNextTier} ${isClient ? t("customer.loyalty.pointsToNextTier") : "points to next tier"}`
                : (isClient ? t("customer.loyalty.highestTierAchieved") : 'Highest tier achieved!')
              }
            </p>
          </div>
          <div className="text-right">
            <p className="text-sm opacity-90">
              {isClient ? t("customer.loyalty.pointsValue") : "Points Value"}
            </p>
            <p className="text-xl font-bold flex items-center gap-1">
              <RiyalSymbol size={16} className="text-white" />
              {calculateRedemptionValue(userProfile.loyaltyPoints).toFixed(2)}
            </p>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1 mb-6">
        {[
          { key: 'redeem', label: isClient ? t("customer.loyalty.redeemPoints") : 'Redeem Points', icon: 'fa-coins' },
          { key: 'scan', label: isClient ? t("customer.loyalty.scanQRCode") : 'Scan Receipt', icon: 'fa-qrcode' },
          { key: 'history', label: isClient ? t("nav.orderHistory") : 'History', icon: 'fa-clock-rotate-left' },
          { key: 'achievements', label: isClient ? t("customer.loyalty.achievements") : 'Achievements', icon: 'fa-trophy' }
        ].map((tab) => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key as any)}
            className={`flex-1 flex items-center justify-center gap-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === tab.key
                ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            <i className={`fa-solid ${tab.icon}`}></i>
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      {activeTab === 'redeem' && (
        <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
          <h3 className="text-xl font-bold mb-6 dark:text-gray-100">
            {isClient ? t("customer.loyalty.redeemPoints") : "Redeem Points"}
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Redemption Form */}
            <div>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {isClient ? t("customer.loyalty.pointsToRedeem") : "Points to Redeem"}
                </label>
                <input
                  type="number"
                  min={loyaltyRules.minRedemptionPoints}
                  max={userProfile.loyaltyPoints}
                  step={loyaltyRules.minRedemptionPoints}
                  value={redemptionAmount}
                  onChange={(e) => setRedemptionAmount(Number(e.target.value))}
                  className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 dark:bg-[#242832] dark:text-gray-100 focus:ring-2 focus:ring-[#66B8B1] focus:border-transparent"
                  placeholder={`Minimum ${loyaltyRules.minRedemptionPoints} points`}
                />
              </div>

              <div className="mb-6">
                <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
                  <span>{isClient ? t("customer.loyalty.redemptionValue") : "Redemption Value"}:</span>
                  <span className="flex items-center gap-1">
                    <RiyalSymbol size={12} />
                    {calculateRedemptionValue(redemptionAmount).toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                  <span>{isClient ? t("customer.loyalty.remainingPoints") : "Remaining Points"}:</span>
                  <span>{(userProfile.loyaltyPoints - redemptionAmount).toLocaleString()}</span>
                </div>
              </div>

              <button
                onClick={handleRedemption}
                disabled={isRedeeming || redemptionAmount < loyaltyRules.minRedemptionPoints || redemptionAmount > userProfile.loyaltyPoints}
                className="w-full bg-[#66B8B1] hover:bg-[#5a9fa0] disabled:bg-gray-300 disabled:cursor-not-allowed text-white py-3 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
              >
                {isRedeeming ? (
                  <>
                    <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
                    {isClient ? t("customer.loyalty.processing") : "Processing..."}
                  </>
                ) : (
                  <>
                    <i className="fa-solid fa-coins"></i>
                    {isClient ? t("customer.loyalty.redeemPoints") : "Redeem Points"}
                  </>
                )}
              </button>
            </div>

            {/* Quick Redemption Options */}
            <div>
              <h4 className="text-lg font-semibold mb-4 dark:text-gray-100">
                {isClient ? t("customer.loyalty.quickRedeem") : "Quick Redeem"}
              </h4>
              <div className="space-y-3">
                {[100, 200, 500, 1000].filter(amount => amount <= userProfile.loyaltyPoints).map((amount) => (
                  <button
                    key={amount}
                    onClick={() => setRedemptionAmount(amount)}
                    className="w-full p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-[#66B8B1] hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors text-left"
                  >
                    <div className="flex justify-between items-center">
                      <span className="font-medium dark:text-gray-100">{amount} Points</span>
                      <span className="text-[#66B8B1] font-semibold flex items-center gap-1">
                        <RiyalSymbol size={12} />
                        {calculateRedemptionValue(amount).toFixed(2)}
                      </span>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'scan' && (
        <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
          <h3 className="text-xl font-bold mb-6 dark:text-gray-100">
            {isClient ? t("customer.loyalty.scanReceipt") : "Scan Receipt QR Code"}
          </h3>

          <div className="text-center py-8">
            <div className="mb-6">
              <i className="fa-solid fa-qrcode text-6xl text-[#66B8B1] mb-4"></i>
              <h4 className="text-lg font-semibold dark:text-gray-100 mb-2">
                {isClient ? t("customer.loyalty.earnPointsOffline") : "Earn Points from Offline Orders"}
              </h4>
              <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
                {isClient ? t("customer.loyalty.scanReceiptDescription") : "Scan the QR code on your receipt from in-store purchases to earn loyalty points retroactively."}
              </p>
            </div>

            <div className="space-y-4 max-w-sm mx-auto">
              <button
                onClick={() => setIsQRScannerOpen(true)}
                disabled={isProcessingReceipt}
                className="w-full bg-[#66B8B1] hover:bg-[#5a9fa0] disabled:bg-gray-300 disabled:cursor-not-allowed text-white py-3 px-6 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
              >
                {isProcessingReceipt ? (
                  <>
                    <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
                    {isClient ? t("customer.loyalty.processing") : "Processing..."}
                  </>
                ) : (
                  <>
                    <i className="fa-solid fa-camera"></i>
                    {isClient ? t("customer.loyalty.scanQRCode") : "Scan QR Code"}
                  </>
                )}
              </button>

              <div className="text-sm text-gray-500 dark:text-gray-400">
                <p className="mb-2">{isClient ? t("customer.loyalty.howItWorks") : "How it works:"}</p>
                <ul className="text-left space-y-1">
                  <li>{isClient ? t("customer.loyalty.step1") : "• Make a purchase at our physical location"}</li>
                  <li>{isClient ? t("customer.loyalty.step2") : "• Find the QR code on your receipt"}</li>
                  <li>{isClient ? t("customer.loyalty.step3") : "• Scan it here to earn points"}</li>
                  <li>{isClient ? t("customer.loyalty.step4") : "• Points are added to your account instantly"}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'history' && (
        <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
          <h3 className="text-xl font-bold mb-6 dark:text-gray-100">
            {isClient ? t("nav.orderHistory") : "Points History"}
          </h3>

          {transactions.length === 0 ? (
            <div className="text-center py-12">
              <i className="fa-solid fa-clock-rotate-left text-4xl text-gray-300 dark:text-gray-600 mb-4"></i>
              <p className="text-gray-500 dark:text-gray-400">
                {isClient ? t("customer.loyalty.noTransactions") : "No transactions yet"}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {transactions.map((transaction) => (
                <div key={transaction.id} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                  <div className="flex items-center gap-4">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                      transaction.type === LoyaltyTransactionType.EARNED
                        ? 'bg-green-100 dark:bg-green-900/20'
                        : 'bg-red-100 dark:bg-red-900/20'
                    }`}>
                      <i className={`fa-solid ${
                        transaction.type === LoyaltyTransactionType.EARNED
                          ? 'fa-plus text-green-600 dark:text-green-400'
                          : 'fa-minus text-red-600 dark:text-red-400'
                      }`}></i>
                    </div>
                    <div>
                      <p className="font-medium dark:text-gray-100">{transaction.description}</p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {formatDate(new Date(transaction.createdAt))}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`font-bold ${
                      transaction.type === LoyaltyTransactionType.EARNED
                        ? 'text-green-600 dark:text-green-400'
                        : 'text-red-600 dark:text-red-400'
                    }`}>
                      {transaction.type === LoyaltyTransactionType.EARNED ? '+' : ''}{transaction.points}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {isClient ? t("customer.loyalty.points") : "points"}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {activeTab === 'achievements' && (
        <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
          <h3 className="text-xl font-bold mb-6 dark:text-gray-100">
            {isClient ? t("customer.loyalty.achievements") : "Achievements"}
          </h3>

          {achievements.length === 0 ? (
            <div className="text-center py-12">
              <i className="fa-solid fa-trophy text-4xl text-gray-300 dark:text-gray-600 mb-4"></i>
              <p className="text-gray-500 dark:text-gray-400">
                {isClient ? t("customer.loyalty.noAchievements") : "No achievements available"}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {achievements.map((achievement) => {
                const isUnlocked = userProfile.achievements.includes(achievement.id);
                return (
                  <div
                    key={achievement.id}
                    className={`p-4 border rounded-lg transition-all ${
                      isUnlocked
                        ? 'border-[#66B8B1] bg-[#66B8B1]/5 dark:bg-[#66B8B1]/10'
                        : 'border-gray-200 dark:border-gray-700 opacity-60'
                    }`}
                  >
                    <div className="text-center">
                      <div className={`text-3xl mb-2 ${isUnlocked ? '' : 'grayscale'}`}>
                        {achievement.icon}
                      </div>
                      <h4 className="font-semibold dark:text-gray-100 mb-1">
                        {isClient && locale === 'ar' && achievement.name_ar ? achievement.name_ar : achievement.name}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        {isClient && locale === 'ar' && achievement.description_ar ? achievement.description_ar : achievement.description}
                      </p>
                      <div className="flex items-center justify-center gap-1 text-sm">
                        <i className="fa-solid fa-coins text-[#66B8B1]"></i>
                        <span className="text-[#66B8B1] font-medium">
                          {achievement.pointsReward} {isClient ? t("customer.loyalty.points") : "points"}
                        </span>
                      </div>
                      {isUnlocked && (
                        <div className="mt-2">
                          <span className="inline-flex items-center gap-1 px-2 py-1 bg-[#66B8B1] text-white text-xs rounded-full">
                            <i className="fa-solid fa-check"></i>
                            {isClient ? t("customer.loyalty.unlocked") : "Unlocked"}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      )}

      {/* QR Scanner Modal */}
      <QRScanner
        isOpen={isQRScannerOpen}
        onScan={handleQRScan}
        onClose={() => setIsQRScannerOpen(false)}
      />
    </DashboardLayout>
  );
}
