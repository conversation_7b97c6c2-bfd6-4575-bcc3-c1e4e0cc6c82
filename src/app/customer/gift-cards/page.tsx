"use client";

import { useEffect, useState, useRef } from "react";
import { useRouter } from "next/navigation";
import { useLocale } from "@/contexts/LocaleContext";
import { useAuth } from "@/hooks/useAuth";
import DashboardLayout from "@/components/customer-dashboard/DashboardLayout";
import EmptyState from "@/components/ui/EmptyState";
import { getUserGiftCards, createGiftCard, getGiftCardByCode } from "@/lib/firebase/firestore";
import { GiftCard } from "@/types/models";
import { formatDate } from "@/lib/utils";

export default function GiftCardsPage() {
  const router = useRouter();
  const { t, isClient } = useLocale();
  const { user, loading, isEmailVerified } = useAuth();
  const [giftCards, setGiftCards] = useState<GiftCard[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  // Purchase form data
  const [purchaseData, setPurchaseData] = useState({
    design: 'classic',
    amount: 25,
    customAmount: ''
  });
  
  // Redeem form data
  const [redeemData, setRedeemData] = useState({
    code: '',
    pin: ''
  });
  
  // Share dialog state
  const [showShareDialog, setShowShareDialog] = useState(false);
  const [selectedCard, setSelectedCard] = useState<GiftCard | null>(null);
  const [shareMessage, setShareMessage] = useState('');
  const [isCopied, setIsCopied] = useState(false);
  const textAreaRef = useRef<HTMLTextAreaElement>(null);
  
  // Modal states
  const [isPurchaseLoading, setIsPurchaseLoading] = useState(false);
  const [isRedeemLoading, setIsRedeemLoading] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [showErrorMessage, setShowErrorMessage] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  
  // Fetch gift cards
  useEffect(() => {
    const fetchGiftCards = async () => {
      if (user?.uid) {
        setIsLoading(true);
        try {
          const userGiftCards = await getUserGiftCards(user.uid);
          setGiftCards(userGiftCards);
        } catch (error) {
          console.error("Error fetching gift cards:", error);
        } finally {
          setIsLoading(false);
        }
      }
    };
    
    if (user) {
      fetchGiftCards();
    }
  }, [user]);
  
  // Redirect to signin if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push("/signin");
    } else if (user && !isEmailVerified) {
      // Redirect to verification page if email is not verified
      router.push("/verify-email");
    }
  }, [loading, user, router]);

  // Handle purchase form input changes
  const handlePurchaseInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPurchaseData(prev => ({ ...prev, [name]: value }));
  };
  
  // Handle redeem form input changes
  const handleRedeemInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setRedeemData(prev => ({ ...prev, [name]: value }));
  };
  
  // Handle design selection
  const handleDesignSelect = (design: string) => {
    setPurchaseData(prev => ({ ...prev, design }));
  };
  
  // Handle amount selection
  const handleAmountSelect = (amount: number) => {
    setPurchaseData(prev => ({ ...prev, amount, customAmount: '' }));
  };
  
  // Generate a random gift card code (in practice, you'd want more robust code generation)
  const generateGiftCardCode = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let code = '';
    for (let i = 0; i < 16; i++) {
      if (i > 0 && i % 4 === 0) code += '-';
      code += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return code;
  };
  
  // Handle gift card purchase
  const handlePurchaseGiftCard = async () => {
    if (!user?.uid) return;
    
    // Validate amount
    const amount = purchaseData.customAmount 
      ? parseFloat(purchaseData.customAmount) 
      : purchaseData.amount;
    
    if (amount <= 0 || isNaN(amount)) {
      setErrorMessage(t("customer.giftCards.invalidAmount"));
      setShowErrorMessage(true);
      setTimeout(() => setShowErrorMessage(false), 3000);
      return;
    }
    
    // Generate card code
    const code = generateGiftCardCode();
    
    setIsPurchaseLoading(true);
    try {
      // Calculate expiry (1 year from now)
      const expiryDate = new Date();
      expiryDate.setFullYear(expiryDate.getFullYear() + 1);
      
      const newGiftCard = await createGiftCard({
        userId: user.uid,
        code,
        initialBalance: amount,
        currentBalance: amount,
        expiryDate,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      // Update state
      setGiftCards(prev => [...prev, newGiftCard]);
      
      // Show success message
      setSuccessMessage(t("customer.giftCards.purchaseSuccess"));
      setShowSuccessMessage(true);
      setTimeout(() => setShowSuccessMessage(false), 3000);
      
      // Reset form
      setPurchaseData({
        design: 'classic',
        amount: 25,
        customAmount: ''
      });
    } catch (error) {
      console.error("Error purchasing gift card:", error);
      setErrorMessage(t("customer.giftCards.purchaseError"));
      setShowErrorMessage(true);
      setTimeout(() => setShowErrorMessage(false), 3000);
    } finally {
      setIsPurchaseLoading(false);
    }
  };
  
  // Handle gift card redemption
  const handleRedeemGiftCard = async () => {
    if (!user?.uid) return;
    
    // Validate input
    if (!redeemData.code.trim()) {
      setErrorMessage(t("customer.giftCards.invalidCode"));
      setShowErrorMessage(true);
      setTimeout(() => setShowErrorMessage(false), 3000);
      return;
    }
    
    setIsRedeemLoading(true);
    try {
      // Check if the gift card exists
      const giftCard = await getGiftCardByCode(redeemData.code.trim());
      
      if (!giftCard) {
        setErrorMessage(t("customer.giftCards.cardNotFound"));
        setShowErrorMessage(true);
        setTimeout(() => setShowErrorMessage(false), 3000);
        return;
      }
      
      // In a real implementation, you would verify the PIN here
      // For this demo, we'll just add the gift card to the user's account if they don't already have it
      
      const existingCard = giftCards.find(card => card.code === giftCard.code);
      
      if (existingCard) {
        setErrorMessage(t("customer.giftCards.alreadyRedeemed"));
        setShowErrorMessage(true);
        setTimeout(() => setShowErrorMessage(false), 3000);
        return;
      }
      
      // Add card to user (in a real app, you would update the card's owner in the database)
      setGiftCards(prev => [...prev, giftCard]);
      
      // Show success message
      setSuccessMessage(t("customer.giftCards.redeemSuccess"));
      setShowSuccessMessage(true);
      setTimeout(() => setShowSuccessMessage(false), 3000);
      
      // Reset form
      setRedeemData({
        code: '',
        pin: ''
      });
    } catch (error) {
      console.error("Error redeeming gift card:", error);
      setErrorMessage(t("customer.giftCards.redeemError"));
      setShowErrorMessage(true);
      setTimeout(() => setShowErrorMessage(false), 3000);
    } finally {
      setIsRedeemLoading(false);
    }
  };
  
  // Format gift card number for display (e.g., •••• 1234)
  const formatGiftCardNumber = (code: string) => {
    const parts = code.split('-');
    if (parts.length > 0) {
      return `•••• ${parts[parts.length - 1]}`;
    }
    return '•••• ••••';
  };

  // Handle copying text to clipboard
  const handleCopyToClipboard = () => {
    if (textAreaRef.current) {
      textAreaRef.current.select();
      document.execCommand('copy');
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    }
  };

  // Handle sharing gift card via dialog
  const handleShareGiftCard = (card: GiftCard) => {
    // Set the selected card
    setSelectedCard(card);
    
    // Get the full card number
    const fullCardNumber = card.code;
    const balanceText = `$${card.currentBalance.toFixed(2)}`;
    const expiryText = card.expiryDate instanceof Date 
      ? formatDate(card.expiryDate) 
      : typeof card.expiryDate === 'string' 
        ? formatDate(new Date(card.expiryDate))
        : '';
    
    // Get sender's name (using user's display name if available)
    const senderName = user?.displayName || t("customer.giftCards.fromFriend");
    
    // Get cafe name
    const cafeName = t("common.barcodeCafe");
    
    // Create the message with all required elements including emojis
    const message = isClient 
      ? t("customer.giftCards.shareGreetingExtended", { 
          senderName,
          cafeName,
          cardNumber: fullCardNumber, 
          balance: balanceText, 
          expiryDate: expiryText 
        })
      : `Surprise! ${senderName} just sent you a gift card from ${cafeName}!\n\nGift Card Details:\nCard Number: ${fullCardNumber}\nBalance: ${balanceText}\nExpires: ${expiryText}\n\nTo redeem, just show this card number at ${cafeName} or enter it in the app. Enjoy your treat!`;
    
    // Set the share message
    setShareMessage(message);
    
    // Show the share dialog
    setShowShareDialog(true);
  };
  
  // Handle WhatsApp share
  const handleWhatsAppShare = () => {
    if (!shareMessage) return;
    
    // Create emoji-safe version of the message for WhatsApp URL sharing
    const emojiSafeMessage = isClient 
      ? t("customer.giftCards.shareGreetingEmojiSafe", { 
          senderName: user?.displayName || t("customer.giftCards.fromFriend"),
          cafeName: t("common.barcodeCafe"),
          cardNumber: selectedCard?.code || '', 
          balance: selectedCard ? `$${selectedCard.currentBalance.toFixed(2)}` : '', 
          expiryDate: selectedCard?.expiryDate instanceof Date 
            ? formatDate(selectedCard.expiryDate) 
            : typeof selectedCard?.expiryDate === 'string' 
              ? formatDate(new Date(selectedCard.expiryDate))
              : ''
        })
      : shareMessage;
    
    // Encode the message for URL
    const encodedMessage = encodeURIComponent(emojiSafeMessage);
    
    // Create the WhatsApp share URL
    const whatsappUrl = `https://wa.me/?text=${encodedMessage}`;
    
    // Open WhatsApp in a new window/tab
    window.open(whatsappUrl, '_blank');
    
    // Close the dialog
    setShowShareDialog(false);
    
    // Show success message
    setSuccessMessage(t("customer.giftCards.shareSuccess") || "Gift card shared successfully!");
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
  };
  
  // Close share dialog
  const closeShareDialog = () => {
    setShowShareDialog(false);
    setSelectedCard(null);
    setShareMessage('');
    setIsCopied(false);
  };

  if (!isClient) {
    return null;
  }

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  return (
    <DashboardLayout>
      {isLoading ? (
        <div className="min-h-[300px] flex items-center justify-center">
          <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
        </div>
      ) : giftCards.length === 0 ? (
        <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm">
          <div className="p-6 border-b border-gray-100 dark:border-gray-800">
            <h1 className="text-2xl font-bold dark:text-gray-100">
              {t("nav.giftCards")}
            </h1>
          </div>
          
          <EmptyState
            icon="fa-solid fa-gift"
            title={isClient ? t("customer.emptyStates.giftCards.title") : "No Gift Cards"}
            message={isClient ? t("customer.emptyStates.giftCards.message") : "You don't have any gift cards yet. Purchase a gift card to enjoy later or share with friends and family."}
            actionLabel={isClient ? t("customer.emptyStates.giftCards.action") : "Get Gift Card"}
            onAction={() => window.scrollTo({ top: document.getElementById('purchase-gift-card')?.offsetTop, behavior: 'smooth' })}
            className="py-16"
          />
        </div>
      ) : null}
      
      <div className="space-y-8">
        <div id="gift-cards-header" className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-100">{t("nav.giftCards")}</h1>
          <p className="text-gray-500 dark:text-gray-400 mt-2">
            {isClient 
              ? t("customer.giftCards.subtitle") 
              : "Purchase new gift cards or manage your existing ones"
            }
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Purchase New Gift Card Section */}
          <div id="purchase-gift-card" className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
            <h2 className="text-xl font-bold mb-6 dark:text-gray-100">
              {isClient 
                ? t("customer.giftCards.purchaseNew") 
                : "Purchase New Gift Card"
              }
            </h2>
            <div className="space-y-6">
              <div id="gift-card-designs" className="grid grid-cols-2 gap-4">
                <div 
                  className={`relative border-2 ${purchaseData.design === 'classic' ? 'border-[#83EAED] dark:border-[#5DBDC0]' : 'border-gray-200 dark:border-gray-700 hover:border-[#83EAED] dark:hover:border-[#5DBDC0]'} rounded-lg p-4 cursor-pointer`}
                  onClick={() => handleDesignSelect('classic')}
                >
                  {purchaseData.design === 'classic' && (
                    <div className="absolute top-2 right-2">
                      <i className="fa-solid fa-check-circle text-[#56999B] dark:text-[#5DBDC0]"></i>
                    </div>
                  )}
                  <img className="w-full h-32 object-cover rounded" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/e6351a183b-1a72288a359ae9472dd7.png" alt="Classic design gift card" />
                  <p className="mt-2 text-sm font-medium dark:text-gray-200">
                    {isClient 
                      ? t("customer.giftCards.classicDesign") 
                      : "Classic Design"
                    }
                  </p>
                </div>
                <div 
                  className={`relative border-2 ${purchaseData.design === 'festive' ? 'border-[#83EAED] dark:border-[#5DBDC0]' : 'border-gray-200 dark:border-gray-700 hover:border-[#83EAED] dark:hover:border-[#5DBDC0]'} rounded-lg p-4 cursor-pointer`}
                  onClick={() => handleDesignSelect('festive')}
                >
                  {purchaseData.design === 'festive' && (
                    <div className="absolute top-2 right-2">
                      <i className="fa-solid fa-check-circle text-[#56999B] dark:text-[#5DBDC0]"></i>
                    </div>
                  )}
                  <img className="w-full h-32 object-cover rounded" src="https://storage.googleapis.com/uxpilot-auth.appspot.com/77538baf14-b6087e93e3e7e2ff51d3.png" alt="Festive design gift card" />
                  <p className="mt-2 text-sm font-medium dark:text-gray-200">
                    {isClient 
                      ? t("customer.giftCards.festiveDesign") 
                      : "Festive Design"
                    }
                  </p>
                </div>
              </div>

              <div id="amount-selection" className="space-y-4">
                <label className="font-medium dark:text-gray-200">
                  {isClient 
                    ? t("customer.giftCards.selectAmount") 
                    : "Select Amount"
                  }
                </label>
                <div className="grid grid-cols-3 gap-4">
                  <button 
                    className={`py-3 px-4 rounded-lg ${purchaseData.amount === 25 && !purchaseData.customAmount ? 'bg-[#83EAED]/20 dark:bg-[#5DBDC0]/20 text-[#56999B] dark:text-[#5DBDC0]' : 'border-2 border-gray-200 dark:border-gray-700 hover:border-[#83EAED] dark:hover:border-[#5DBDC0] dark:text-gray-200'} font-medium`}
                    onClick={() => handleAmountSelect(25)}
                  >
                    $25
                  </button>
                  <button 
                    className={`py-3 px-4 rounded-lg ${purchaseData.amount === 50 && !purchaseData.customAmount ? 'bg-[#83EAED]/20 dark:bg-[#5DBDC0]/20 text-[#56999B] dark:text-[#5DBDC0]' : 'border-2 border-gray-200 dark:border-gray-700 hover:border-[#83EAED] dark:hover:border-[#5DBDC0] dark:text-gray-200'} font-medium`}
                    onClick={() => handleAmountSelect(50)}
                  >
                    $50
                  </button>
                  <button 
                    className={`py-3 px-4 rounded-lg ${purchaseData.amount === 100 && !purchaseData.customAmount ? 'bg-[#83EAED]/20 dark:bg-[#5DBDC0]/20 text-[#56999B] dark:text-[#5DBDC0]' : 'border-2 border-gray-200 dark:border-gray-700 hover:border-[#83EAED] dark:hover:border-[#5DBDC0] dark:text-gray-200'} font-medium`}
                    onClick={() => handleAmountSelect(100)}
                  >
                    $100
                  </button>
                </div>
                <div className="relative">
                  <input 
                    type="text" 
                    name="customAmount"
                    value={purchaseData.customAmount}
                    onChange={handlePurchaseInputChange}
                    placeholder={isClient ? t("customer.giftCards.customAmount") : "Custom Amount"} 
                    className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 dark:border-gray-700 focus:border-[#83EAED] dark:focus:border-[#5DBDC0] focus:outline-none dark:bg-[#242832] dark:text-gray-100" 
                  />
                </div>
              </div>

              <button 
                className="w-full bg-[#83EAED] dark:bg-[#5DBDC0] text-white py-3 rounded-lg hover:bg-[#83EAED]/90 dark:hover:bg-[#4A9EA0] font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                onClick={handlePurchaseGiftCard}
                disabled={isPurchaseLoading}
              >
                {isPurchaseLoading ? (
                  <>
                    <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                    {isClient ? t("customer.giftCards.processing") : "Processing..."}
                  </>
                ) : (
                  isClient ? t("customer.giftCards.purchaseButton") : "Purchase Gift Card"
                )}
              </button>
            </div>
          </div>

          {/* Manage Existing Gift Cards Section */}
          <div id="manage-gift-cards" className="space-y-6">
            <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
              <h2 className="text-xl font-bold mb-6 dark:text-gray-100">
                {isClient 
                  ? t("customer.giftCards.yourGiftCards") 
                  : "Your Gift Cards"
                }
              </h2>
              {giftCards.length === 0 ? (
                <div className="text-center py-6">
                  <p className="text-gray-500 dark:text-gray-400">
                    {isClient 
                      ? t("customer.giftCards.noCardsYet") 
                      : "You don't have any gift cards yet."
                    }
                  </p>
                </div>
              ) : (
                <div id="active-gift-cards" className="space-y-4">
                  {giftCards.map(card => (
                    <div key={card.id} className="border-2 border-gray-100 dark:border-gray-700 rounded-lg p-4">
                      <div className="flex justify-between items-center">
                        <div>
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            {isClient 
                              ? t("customer.giftCards.cardEndingIn") 
                              : "Card ending in"
                            }
                          </span>
                          <p className="font-medium dark:text-gray-100">{formatGiftCardNumber(card.code)}</p>
                        </div>
                        <div className="text-right">
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            {isClient 
                              ? t("customer.giftCards.balance") 
                              : "Balance"
                            }
                          </span>
                          <p className="font-medium dark:text-gray-100">${card.currentBalance.toFixed(2)}</p>
                        </div>
                      </div>
                      <div className="mt-2">
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {isClient 
                            ? t("customer.giftCards.expiresOn") 
                            : "Expires on"
                          } {' '}
                          {card.expiryDate instanceof Date 
                            ? formatDate(card.expiryDate) 
                            : typeof card.expiryDate === 'string' 
                              ? formatDate(new Date(card.expiryDate))
                              : ''}
                        </span>
                      </div>
                      <div className="mt-4 flex gap-2">
                        <button className="text-sm text-[#56999B] dark:text-[#5DBDC0] hover:underline">
                          {isClient 
                            ? t("customer.giftCards.viewDetails") 
                            : "View Details"
                          }
                        </button>
                        <span className="text-gray-300 dark:text-gray-600">|</span>
                        <button 
                          className="text-sm text-[#56999B] dark:text-[#5DBDC0] hover:underline"
                          onClick={() => handleShareGiftCard(card)}
                        >
                          {isClient 
                            ? t("customer.giftCards.share") 
                            : "Share"
                          }
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div id="redeem-gift-card" className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
              <h2 className="text-xl font-bold mb-6 dark:text-gray-100">
                {isClient 
                  ? t("customer.giftCards.redeemGiftCard") 
                  : "Redeem Gift Card"
                }
              </h2>
              <div className="space-y-4">
                <input 
                  type="text" 
                  name="code"
                  value={redeemData.code}
                  onChange={handleRedeemInputChange}
                  placeholder={isClient ? t("customer.giftCards.enterGiftCardNumber") : "Enter Gift Card Number"} 
                  className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 dark:border-gray-700 focus:border-[#83EAED] dark:focus:border-[#5DBDC0] focus:outline-none dark:bg-[#242832] dark:text-gray-100" 
                />
                <input 
                  type="text" 
                  name="pin"
                  value={redeemData.pin}
                  onChange={handleRedeemInputChange}
                  placeholder={isClient ? t("customer.giftCards.enterPIN") : "Enter PIN"} 
                  className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 dark:border-gray-700 focus:border-[#83EAED] dark:focus:border-[#5DBDC0] focus:outline-none dark:bg-[#242832] dark:text-gray-100" 
                />
                <button 
                  className="w-full bg-[#83EAED] dark:bg-[#5DBDC0] text-white py-3 rounded-lg hover:bg-[#83EAED]/90 dark:hover:bg-[#4A9EA0] font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                  onClick={handleRedeemGiftCard}
                  disabled={isRedeemLoading}
                >
                  {isRedeemLoading ? (
                    <>
                      <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                      {isClient ? t("customer.giftCards.processing") : "Processing..."}
                    </>
                  ) : (
                    isClient ? t("customer.giftCards.redeemButton") : "Redeem Card"
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Success Message */}
      {showSuccessMessage && (
        <div className="fixed bottom-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded shadow-md">
          <div className="flex items-center">
            <i className="fa-solid fa-check-circle mr-2"></i>
            <span>{successMessage}</span>
          </div>
        </div>
      )}
      
      {/* Error Message */}
      {showErrorMessage && (
        <div className="fixed bottom-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded shadow-md">
          <div className="flex items-center">
            <i className="fa-solid fa-exclamation-circle mr-2"></i>
            <span>{errorMessage}</span>
          </div>
        </div>
      )}
      
      {/* Share Dialog Modal */}
      {showShareDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-lg max-w-lg w-full">
            <div className="p-6 border-b border-gray-100 dark:border-gray-800">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-bold dark:text-gray-100">
                  {isClient ? t("customer.giftCards.shareCard") : "Share Gift Card"}
                </h2>
                <button 
                  onClick={closeShareDialog}
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  <i className="fa-solid fa-times"></i>
                </button>
              </div>
            </div>
            
            <div className="p-6">
              <div className="mb-4">
                <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                  {isClient ? t("customer.giftCards.shareMessage") : "Here's your gift card message:"}
                </p>
                <div className="relative">
                  <textarea
                    ref={textAreaRef}
                    value={shareMessage}
                    readOnly
                    className="w-full px-4 py-3 rounded-lg border-2 border-gray-200 dark:border-gray-700 focus:border-[#83EAED] dark:focus:border-[#5DBDC0] focus:outline-none dark:bg-[#242832] dark:text-gray-100 h-48 resize-none"
                  />
                  <button
                    onClick={handleCopyToClipboard}
                    className="absolute top-2 right-2 p-2 bg-gray-100 dark:bg-gray-700 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                    title={isClient ? t("customer.giftCards.copyToClipboard") : "Copy to clipboard"}
                  >
                    <i className={`fa-solid ${isCopied ? 'fa-check text-green-500' : 'fa-copy text-gray-500 dark:text-gray-300'}`}></i>
                  </button>
                </div>
                {isCopied && (
                  <p className="text-sm text-green-500 mt-1">
                    {isClient ? t("customer.giftCards.copied") : "Copied!"}
                  </p>
                )}
              </div>
              
              <div className="flex gap-4 mt-6">
                <button
                  onClick={closeShareDialog}
                  className="flex-1 py-3 px-4 border-2 border-gray-200 dark:border-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 font-medium"
                >
                  {isClient ? t("common.cancel") : "Cancel"}
                </button>
                <button
                  onClick={handleWhatsAppShare}
                  className="flex-1 bg-[#25D366] text-white py-3 px-4 rounded-lg hover:bg-[#1ebea5] font-medium flex items-center justify-center"
                >
                  <i className="fa-brands fa-whatsapp mr-2 text-lg"></i>
                  {isClient ? t("customer.giftCards.shareWhatsApp") : "Share via WhatsApp"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
} 