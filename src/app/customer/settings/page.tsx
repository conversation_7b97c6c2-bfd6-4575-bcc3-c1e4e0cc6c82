"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useLocale } from "@/contexts/LocaleContext";
import { useAuth } from "@/hooks/useAuth";
import DashboardLayout from "@/components/customer-dashboard/DashboardLayout";
import EmptyState from "@/components/ui/EmptyState";

export default function SettingsPage() {
  const router = useRouter();
  const { t, isClient, dir } = useLocale();
  const { user, loading, isEmailVerified } = useAuth();
  const [hasSettings, setHasSettings] = useState(false); // Set to false to show empty state
  
  // Redirect to signin if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push("/signin");
    } else if (user && !isEmailVerified) {
      // Redirect to verification page if email is not verified
      router.push("/verify-email");
    }
  }, [loading, user, router]);

  const handleUpdateSettings = () => {
    // Placeholder for settings update action
    setHasSettings(true);
  };

  if (!isClient) {
    return null;
  }

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  return (
    <DashboardLayout>
      <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm">
        <div className="p-6 border-b border-gray-100 dark:border-gray-800">
          <h1 className="text-2xl font-bold dark:text-gray-100">
            {t("nav.settings")}
          </h1>
        </div>
        
        {hasSettings ? (
          <div className="p-6">
            {/* Settings content will go here */}
            <p>Settings Content</p>
          </div>
        ) : (
          <EmptyState
            icon="fa-solid fa-gear"
            title={isClient ? t("customer.emptyStates.settings.title") : "Settings"}
            message={isClient ? t("customer.emptyStates.settings.message") : "Manage your account settings including notifications, language, and theme preferences."}
            actionLabel={isClient ? t("customer.emptyStates.settings.action") : "Update Settings"}
            onAction={handleUpdateSettings}
            className="py-16"
          />
        )}
      </div>
    </DashboardLayout>
  );
} 