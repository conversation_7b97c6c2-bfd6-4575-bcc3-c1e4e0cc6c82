"use client";

import AdminDashboardLayout from "@/components/admin-dashboard/DashboardLayout";
import { useLocale } from "@/contexts/LocaleContext";
import { useAuth } from "@/hooks/useAuth";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { ReactNode } from "react";

export default function AdminLayout({ children }: { children: ReactNode }) {
  const { isClient } = useLocale();
  const { loading, isAdmin, user, adminCheckComplete } = useAuth();
  const router = useRouter();
  const [isChecked, setIsChecked] = useState(false);
  const [adminCheckTimeout, setAdminCheckTimeout] = useState<NodeJS.Timeout | null>(null);

  // Check if user is an admin - only redirect once
  useEffect(() => {
    console.log("AdminLayout: Auth state check", {
      loading,
      isAdmin,
      adminCheckComplete,
      userId: user?.uid,
      isChecked
    });

    // Only check once when loading is complete, user exists, and admin check is complete
    if (!loading && user && adminCheckComplete && !isChecked) {
      if (!isAdmin) {
        console.log("AdminLayout: User is not admin, redirecting to customer dashboard");
        router.replace('/customer/dashboard');
      } else {
        console.log("AdminLayout: User is admin, rendering admin layout");
      }
      setIsChecked(true);
    }
  }, [loading, isAdmin, adminCheckComplete, user, router, isChecked]);

  // Add timeout for admin check to prevent infinite loading
  useEffect(() => {
    if (!loading && user && !adminCheckComplete && !isChecked) {
      const timeout = setTimeout(() => {
        console.warn("AdminLayout: Admin check timeout, redirecting to customer dashboard");
        router.replace('/customer/dashboard');
        setIsChecked(true);
      }, 5000); // 5 second timeout

      setAdminCheckTimeout(timeout);
      return () => clearTimeout(timeout);
    }

    // Clear timeout if check is complete
    if ((isChecked || adminCheckComplete) && adminCheckTimeout) {
      clearTimeout(adminCheckTimeout);
      setAdminCheckTimeout(null);
    }
  }, [loading, user, adminCheckComplete, isChecked, router, adminCheckTimeout]);

  // Show loading while checking authentication or client-side rendering
  if (!isClient || loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  // Show loading while checking admin status (but only briefly)
  if (!adminCheckComplete || !isChecked) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  // Don't render admin content if not an admin (this should rarely be reached due to redirect)
  if (!isAdmin) {
    console.log("AdminLayout: Final check - user is not admin, showing loading while redirecting");
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  return <AdminDashboardLayout>{children}</AdminDashboardLayout>;
}