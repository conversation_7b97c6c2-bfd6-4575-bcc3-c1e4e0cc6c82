"use client";

import { useEffect, useState } from "react";
import { useLocale } from "@/contexts/LocaleContext";
import {
  getUserMenuItemCount,
  getActiveCategoryCount,
  getTotalOrdersCount,
  getTodaysSales,
  getRecentOrdersForAdmin
} from "@/lib/firebase/firestore";
import { useAuth } from "@/hooks/useAuth";
import Link from "next/link";
import { Order, OrderStatus } from "@/types/models";
import { formatDate } from "@/lib/utils";
import RiyalSymbol from "@/components/ui/RiyalSymbol";

export default function AdminDashboardPage() {
  const { t, isClient } = useLocale();
  const { user } = useAuth();
  
  const [menuItemCount, setMenuItemCount] = useState<number | null>(null);
  const [categoryCount, setCategoryCount] = useState<number | null>(null);
  const [totalOrdersCount, setTotalOrdersCount] = useState<number | null>(null);
  const [todaysSales, setTodaysSales] = useState<number | null>(null);
  const [recentOrders, setRecentOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    if (user?.uid) {
      fetchDashboardData();
    }
  }, [user?.uid]);
  
  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);

      // Fetch all dashboard data in parallel
      const [itemCount, activeCats, ordersCount, sales, orders] = await Promise.all([
        getUserMenuItemCount(user!.uid),
        getActiveCategoryCount(user!.uid),
        getTotalOrdersCount(),
        getTodaysSales(),
        getRecentOrdersForAdmin(3)
      ]);

      setMenuItemCount(itemCount);
      setCategoryCount(activeCats);
      setTotalOrdersCount(ordersCount);
      setTodaysSales(sales);
      setRecentOrders(orders);
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Display skeleton loader when counts are loading
  const renderCount = (count: number | null) => {
    if (isLoading) {
      return (
        <div className="h-8 w-16 bg-gray-200 dark:bg-gray-700 animate-pulse rounded"></div>
      );
    }
    return count !== null ? count : '0';
  };
  
  // Helper functions for order status
  const getOrderStatusLabel = (status: OrderStatus): string => {
    switch (status) {
      case OrderStatus.ORDER_PLACED:
        return isClient ? t("admin.orderStatusTypes.new") : "New";
      case OrderStatus.PREPARING:
        return isClient ? t("admin.orderStatusTypes.processing") : "Processing";
      case OrderStatus.READY_FOR_PICKUP:
        return isClient ? t("admin.orderStatusTypes.readyForPickup") : "Ready for Pickup";
      case OrderStatus.OUT_FOR_DELIVERY:
        return isClient ? t("admin.orderStatusTypes.outForDelivery") : "Out for Delivery";
      case OrderStatus.DELIVERED:
        return isClient ? t("admin.orderStatusTypes.completed") : "Completed";
      case OrderStatus.CANCELLED:
        return isClient ? t("admin.orderStatusTypes.cancelled") : "Cancelled";
      default:
        return status;
    }
  };

  const getStatusBadgeClass = (status: OrderStatus): string => {
    switch (status) {
      case OrderStatus.ORDER_PLACED:
        return "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400";
      case OrderStatus.PREPARING:
        return "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400";
      case OrderStatus.READY_FOR_PICKUP:
        return "bg-teal-100 dark:bg-teal-900/30 text-teal-700 dark:text-teal-400";
      case OrderStatus.OUT_FOR_DELIVERY:
        return "bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400";
      case OrderStatus.DELIVERED:
        return "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400";
      case OrderStatus.CANCELLED:
        return "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400";
      default:
        return "bg-gray-100 dark:bg-gray-900/30 text-gray-700 dark:text-gray-400";
    }
  };

  return (
    <div className="space-y-8">
      {/* Dashboard Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
          {isClient ? t('admin.dashboard') : 'Dashboard Overview'}
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          {isClient ? t('admin.welcomeMessage') : 'Welcome back! Here&apos;s what&apos;s happening with your cafe today.'}
        </p>
      </div>
      
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white dark:bg-[#1d2127] p-6 rounded-xl shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-500 dark:text-gray-400 text-sm">{isClient ? t('admin.stats.today') : "Today's Sales"}</p>
              {isLoading ? (
                <div className="h-8 w-20 bg-gray-200 dark:bg-gray-700 animate-pulse rounded"></div>
              ) : (
                <h3 className="text-2xl font-bold text-gray-800 dark:text-white flex items-center gap-1">
                  <RiyalSymbol size={20} className="text-gray-800 dark:text-white" />
                  {todaysSales?.toFixed(2) || '0.00'}
                </h3>
              )}
            </div>
            <i className="fa-solid fa-dollar-sign text-[#56999B] dark:text-[#5DBDC0] text-2xl"></i>
          </div>
        </div>

        <div className="bg-white dark:bg-[#1d2127] p-6 rounded-xl shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-500 dark:text-gray-400 text-sm">{isClient ? t('admin.totalOrders') : 'Total Orders'}</p>
              <h3 className="text-2xl font-bold text-gray-800 dark:text-white">{renderCount(totalOrdersCount)}</h3>
            </div>
            <i className="fa-solid fa-shopping-bag text-[#56999B] dark:text-[#5DBDC0] text-2xl"></i>
          </div>
        </div>

        <div className="bg-white dark:bg-[#1d2127] p-6 rounded-xl shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-500 dark:text-gray-400 text-sm">{isClient ? t('admin.activeMenuItems') : 'Active Menu Items'}</p>
              <h3 className="text-2xl font-bold text-gray-800 dark:text-white">{renderCount(menuItemCount)}</h3>
            </div>
            <i className="fa-solid fa-utensils text-[#56999B] dark:text-[#5DBDC0] text-2xl"></i>
          </div>
        </div>

        <div className="bg-white dark:bg-[#1d2127] p-6 rounded-xl shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-500 dark:text-gray-400 text-sm">{isClient ? t('admin.categories') : 'Categories'}</p>
              <h3 className="text-2xl font-bold text-gray-800 dark:text-white">{renderCount(categoryCount)}</h3>
            </div>
            <i className="fa-solid fa-th-large text-[#56999B] dark:text-[#5DBDC0] text-2xl"></i>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6 mb-8">
        <h2 className="text-xl font-bold text-gray-800 dark:text-white mb-4">{isClient ? t('admin.quickActions') : 'Quick Actions'}</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Link 
            href="/admin/menu-items/add"
            className="flex flex-col items-center justify-center p-4 border border-[#83EAED] dark:border-[#5DBDC0] rounded-lg hover:bg-[#A8FDFF]/20 dark:hover:bg-[#5DBDC0]/20 transition-colors"
          >
            <i className="fa-solid fa-plus-circle text-[#56999B] dark:text-[#5DBDC0] text-2xl mb-2"></i>
            <span className="text-sm text-gray-600 dark:text-gray-300">{isClient ? t('admin.addItemMenu') : 'Add Menu Item'}</span>
          </Link>
          <Link 
            href="/admin/categories"
            className="flex flex-col items-center justify-center p-4 border border-[#83EAED] dark:border-[#5DBDC0] rounded-lg hover:bg-[#A8FDFF]/20 dark:hover:bg-[#5DBDC0]/20 transition-colors"
          >
            <i className="fa-solid fa-th-large text-[#56999B] dark:text-[#5DBDC0] text-2xl mb-2"></i>
            <span className="text-sm text-gray-600 dark:text-gray-300">{isClient ? t('admin.manageCategories') : 'Manage Categories'}</span>
          </Link>
          <Link 
            href="/admin/qr-generator"
            className="flex flex-col items-center justify-center p-4 border border-[#83EAED] dark:border-[#5DBDC0] rounded-lg hover:bg-[#A8FDFF]/20 dark:hover:bg-[#5DBDC0]/20 transition-colors"
          >
            <i className="fa-solid fa-qrcode text-[#56999B] dark:text-[#5DBDC0] text-2xl mb-2"></i>
            <span className="text-sm text-gray-600 dark:text-gray-300">{isClient ? t('admin.generateQR') : 'Generate QR'}</span>
          </Link>
          <Link 
            href="/admin/menu-items"
            className="flex flex-col items-center justify-center p-4 border border-[#83EAED] dark:border-[#5DBDC0] rounded-lg hover:bg-[#A8FDFF]/20 dark:hover:bg-[#5DBDC0]/20 transition-colors"
          >
            <i className="fa-solid fa-utensils text-[#56999B] dark:text-[#5DBDC0] text-2xl mb-2"></i>
            <span className="text-sm text-gray-600 dark:text-gray-300">{isClient ? t('admin.viewMenuItems') : 'View Menu Items'}</span>
          </Link>
        </div>
      </div>

      {/* Recent Orders */}
      <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-bold text-gray-800 dark:text-white">{isClient ? t('admin.recentOrders') : 'Recent Orders'}</h2>
          <Link href="/admin/orders" className="text-[#56999B] dark:text-[#5DBDC0] hover:text-[#74C8CA]">
            {isClient ? t('admin.viewAll') : 'View All'}
          </Link>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="text-left text-sm text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700">
                <th className="pb-3">{isClient ? t('admin.orderId') : 'Order ID'}</th>
                <th className="pb-3">{isClient ? t('admin.customer') : 'Customer'}</th>
                <th className="pb-3">{isClient ? t('admin.items') : 'Items'}</th>
                <th className="pb-3">{isClient ? t('admin.total') : 'Total'}</th>
                <th className="pb-3">{isClient ? t('admin.statusColumn') : 'Status'}</th>
                <th className="pb-3">{isClient ? t('admin.actionColumn') : 'Action'}</th>
              </tr>
            </thead>
            <tbody className="text-sm">
              {isLoading ? (
                // Loading skeleton
                Array.from({ length: 3 }).map((_, index) => (
                  <tr key={index} className="border-b border-gray-200 dark:border-gray-700">
                    <td className="py-3"><div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 animate-pulse rounded"></div></td>
                    <td className="py-3"><div className="h-4 w-24 bg-gray-200 dark:bg-gray-700 animate-pulse rounded"></div></td>
                    <td className="py-3"><div className="h-4 w-16 bg-gray-200 dark:bg-gray-700 animate-pulse rounded"></div></td>
                    <td className="py-3"><div className="h-4 w-16 bg-gray-200 dark:bg-gray-700 animate-pulse rounded"></div></td>
                    <td className="py-3"><div className="h-6 w-20 bg-gray-200 dark:bg-gray-700 animate-pulse rounded-full"></div></td>
                    <td className="py-3"><div className="h-4 w-12 bg-gray-200 dark:bg-gray-700 animate-pulse rounded"></div></td>
                  </tr>
                ))
              ) : recentOrders.length > 0 ? (
                recentOrders.map((order, index) => (
                  <tr key={order.id} className={index < recentOrders.length - 1 ? "border-b border-gray-200 dark:border-gray-700" : ""}>
                    <td className="py-3 text-gray-800 dark:text-gray-200">#{order.id.slice(-8).toUpperCase()}</td>
                    <td className="py-3 text-gray-800 dark:text-gray-200">{order.customerName || 'Customer'}</td>
                    <td className="py-3 text-gray-800 dark:text-gray-200">
                      {order.items.length} {order.items.length === 1
                        ? (isClient ? t('admin.itemCount') : 'item')
                        : (isClient ? t('admin.itemsCount') : 'items')
                      }
                    </td>
                    <td className="py-3 text-gray-800 dark:text-gray-200 flex items-center gap-1">
                      <RiyalSymbol size={12} className="text-gray-800 dark:text-gray-200" />
                      {order.total.toFixed(2)}
                    </td>
                    <td className="py-3">
                      <span className={`px-2 py-1 rounded-full text-xs ${getStatusBadgeClass(order.status)}`}>
                        {getOrderStatusLabel(order.status)}
                      </span>
                    </td>
                    <td className="py-3">
                      <Link href={`/admin/orders`} className="text-[#56999B] dark:text-[#5DBDC0] hover:underline">
                        {isClient ? t('admin.view') : 'View'}
                      </Link>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={6} className="py-8 text-center text-gray-500 dark:text-gray-400">
                    {isClient ? t('admin.noRecentOrders') : 'No recent orders found'}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
} 