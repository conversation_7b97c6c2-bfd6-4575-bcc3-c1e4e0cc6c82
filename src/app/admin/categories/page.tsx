"use client";

import { useState, useEffect } from 'react';
import { useLocale } from "@/contexts/LocaleContext";
import { useAuth } from "@/hooks/useAuth";
import { toast } from "@/hooks/use-toast";
import { 
  getUserCategories, 
  createCategory, 
  updateCategory, 
  deleteCategory,
  getCategoryCount,
  getActiveCategoryCount,
  getFeaturedCategoryCount
} from "@/lib/firebase/firestore";
import { Category } from "@/types/models";

// Modal component for adding/editing categories
const CategoryModal = ({ 
  isOpen, 
  onClose, 
  category, 
  onSave, 
  isClient, 
  t 
}: { 
  isOpen: boolean; 
  onClose: () => void; 
  category?: Category; 
  onSave: (data: Partial<Category>) => void; 
  isClient: boolean; 
  t: (key: string, params?: Record<string, string>) => string; 
}) => {
  const [name, setName] = useState('');
  const [name_ar, setNameAr] = useState('');
  const [description, setDescription] = useState('');
  const [description_ar, setDescriptionAr] = useState('');
  const [icon, setIcon] = useState('fa-mug-hot');
  const [availableFrom, setAvailableFrom] = useState('');
  const [availableTo, setAvailableTo] = useState('');
  const [isActive, setIsActive] = useState(true);
  const [isVisible, setIsVisible] = useState(true);
  const [isFeatured, setIsFeatured] = useState(false);

  useEffect(() => {
    if (category) {
      setName(category.name);
      setNameAr(category.name_ar || '');
      setDescription(category.description || '');
      setDescriptionAr(category.description_ar || '');
      setIcon(category.icon);
      setAvailableFrom(category.availableFrom || '');
      setAvailableTo(category.availableTo || '');
      setIsActive(category.isActive);
      setIsVisible(category.isVisible);
      setIsFeatured(category.isFeatured);
    } else {
      // Reset form for new category
      setName('');
      setNameAr('');
      setDescription('');
      setDescriptionAr('');
      setIcon('fa-mug-hot');
      setAvailableFrom('');
      setAvailableTo('');
      setIsActive(true);
      setIsVisible(true);
      setIsFeatured(false);
    }
  }, [category, isOpen]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    onSave({
      name,
      name_ar: name_ar || undefined,
      description: description || undefined,
      description_ar: description_ar || undefined,
      icon,
      availableFrom,
      availableTo,
      isActive,
      isVisible,
      isFeatured
    });
  };

  if (!isOpen) return null;

  const iconOptions = [
    { value: 'fa-mug-hot', label: isClient ? t('admin.iconOptions.hotBeverage') : 'Hot Beverage' },
    { value: 'fa-burger', label: isClient ? t('admin.iconOptions.burger') : 'Burger' },
    { value: 'fa-pizza-slice', label: isClient ? t('admin.iconOptions.pizza') : 'Pizza' },
    { value: 'fa-cake-candles', label: isClient ? t('admin.iconOptions.dessert') : 'Dessert' },
    { value: 'fa-utensils', label: isClient ? t('admin.iconOptions.utensils') : 'Utensils' },
    { value: 'fa-martini-glass-citrus', label: isClient ? t('admin.iconOptions.drinks') : 'Drinks' },
    { value: 'fa-ice-cream', label: isClient ? t('admin.iconOptions.iceCream') : 'Ice Cream' },
    { value: 'fa-fish', label: isClient ? t('admin.iconOptions.fish') : 'Fish' },
    { value: 'fa-drumstick-bite', label: isClient ? t('admin.iconOptions.chicken') : 'Chicken' },
  ];

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="bg-white dark:bg-[#1d2127] rounded-xl max-w-md w-full p-6">
        <h2 className="text-xl font-bold text-gray-800 dark:text-white mb-4">
          {category ? 
            (isClient ? t('admin.editCategory') : 'Edit Category') : 
            (isClient ? t('admin.addCategory') : 'Add Category')}
        </h2>
        
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {isClient ? t('admin.categoryName') : 'Category Name'} *
              </label>
              <input
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-lg dark:bg-[#2a2e35] dark:text-white"
                placeholder={isClient ? t('admin.categoryNamePlaceholder') : 'e.g., Hot Beverages'}
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {isClient ? t('admin.categoryNameArabic') : 'Category Name (Arabic)'}
              </label>
              <input
                type="text"
                value={name_ar}
                onChange={(e) => setNameAr(e.target.value)}
                className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-lg dark:bg-[#2a2e35] dark:text-white"
                placeholder={isClient ? t('admin.categoryNameArabicPlaceholder') : 'مثال: المشروبات الساخنة'}
                dir="rtl"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {isClient ? t('admin.categoryDescription') : 'Category Description'}
              </label>
              <textarea
                rows={3}
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-lg dark:bg-[#2a2e35] dark:text-white"
                placeholder={isClient ? t('admin.categoryDescriptionPlaceholder') : 'Describe this category...'}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {isClient ? t('admin.categoryDescriptionArabic') : 'Category Description (Arabic)'}
              </label>
              <textarea
                rows={3}
                value={description_ar}
                onChange={(e) => setDescriptionAr(e.target.value)}
                className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-lg dark:bg-[#2a2e35] dark:text-white"
                placeholder={isClient ? t('admin.categoryDescriptionArabicPlaceholder') : 'وصف هذه الفئة بالعربية...'}
                dir="rtl"
              />
            </div>
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {isClient ? t('admin.icon') : 'Icon'}
            </label>
            <select
              value={icon}
              onChange={(e) => setIcon(e.target.value)}
              className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-lg dark:bg-[#2a2e35] dark:text-white"
            >
              {iconOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <div className="mt-2 flex items-center">
              <div className="w-10 h-10 bg-[#A8FDFF] dark:bg-[#5DBDC0]/30 rounded-full flex items-center justify-center">
                <i className={`fa-solid ${icon} text-[#56999B] dark:text-[#5DBDC0] text-xl`}></i>
              </div>
              <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
                {isClient ? t('admin.iconOptions.preview') : 'Preview'}
              </span>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {isClient ? t('admin.availableFrom') : 'Available From'}
              </label>
              <input
                type="time"
                value={availableFrom}
                onChange={(e) => setAvailableFrom(e.target.value)}
                className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-lg dark:bg-[#2a2e35] dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                {isClient ? t('admin.availableTo') : 'Available To'}
              </label>
              <input
                type="time"
                value={availableTo}
                onChange={(e) => setAvailableTo(e.target.value)}
                className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-lg dark:bg-[#2a2e35] dark:text-white"
              />
            </div>
          </div>

          <div className="flex space-x-4 mb-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={isActive}
                onChange={(e) => setIsActive(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                {isClient ? t('admin.isActive') : 'Active'}
              </span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={isVisible}
                onChange={(e) => setIsVisible(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                {isClient ? t('admin.isVisible') : 'Visible'}
              </span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={isFeatured}
                onChange={(e) => setIsFeatured(e.target.checked)}
                className="mr-2"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                {isClient ? t('admin.isFeatured') : 'Featured'}
              </span>
            </label>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
            >
              {isClient ? t('admin.cancel') : 'Cancel'}
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-[#56999B] dark:bg-[#5DBDC0] text-white rounded-lg hover:bg-[#74C8CA] dark:hover:bg-[#4A9A9D]"
            >
              {isClient ? t('admin.save') : 'Save'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Delete confirmation modal
const DeleteConfirmationModal = ({ 
  isOpen, 
  onClose, 
  onConfirm, 
  isClient, 
  t 
}: { 
  isOpen: boolean; 
  onClose: () => void; 
  onConfirm: () => void; 
  isClient: boolean; 
  t: (key: string, params?: Record<string, string>) => string; 
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="bg-white dark:bg-[#1d2127] rounded-xl max-w-md w-full p-6">
        <h2 className="text-xl font-bold text-gray-800 dark:text-white mb-4">
          {isClient ? t('admin.deleteCategoryConfirmation') : 'Delete Category Confirmation'}
        </h2>
        
        <p className="text-gray-700 dark:text-gray-300 mb-4">
          {isClient ? t('admin.deleteCategoryConfirmationMessage') : 'Are you sure you want to delete this category?'}
        </p>

        <div className="flex justify-end space-x-2">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 dark:border-gray-700 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800"
          >
            {isClient ? t('admin.cancel') : 'Cancel'}
          </button>
          <button
            type="button"
            onClick={onConfirm}
            className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600"
          >
            {isClient ? t('admin.delete') : 'Delete'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default function CategoriesPage() {
  const { t, isClient } = useLocale();
  const { user } = useAuth();
  
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [modalOpen, setModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [currentCategory, setCurrentCategory] = useState<Category | undefined>(undefined);
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    featured: 0
  });

  // Load categories
  useEffect(() => {
    if (user) {
      fetchCategories();
      fetchStats();
    }
  }, [user]);

  const fetchCategories = async () => {
    if (!user) return;
    
    setIsLoading(true);
    try {
      const fetchedCategories = await getUserCategories(user.uid);
      setCategories(fetchedCategories);
    } catch (error) {
      console.error('Error fetching categories:', error);
      toast({
        variant: "destructive",
        title: isClient ? t('admin.errorFetchingCategories') : 'Error fetching categories',
        description: String(error),
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchStats = async () => {
    if (!user) return;
    
    try {
      const [total, active, featured] = await Promise.all([
        getCategoryCount(user.uid),
        getActiveCategoryCount(user.uid),
        getFeaturedCategoryCount(user.uid)
      ]);
      
      setStats({ total, active, featured });
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const handleAddCategory = () => {
    setCurrentCategory(undefined);
    setModalOpen(true);
  };

  const handleEditCategory = (category: Category) => {
    setCurrentCategory(category);
    setModalOpen(true);
  };

  const handleDeleteClick = (category: Category) => {
    setCurrentCategory(category);
    setDeleteModalOpen(true);
  };

  const handleSaveCategory = async (data: Partial<Category>) => {
    if (!user) return;
    
    try {
      if (currentCategory) {
        // Update existing category
        await updateCategory(currentCategory.id, data);
        toast({
          title: isClient ? t('admin.categoryUpdated') : 'Category updated',
          description: isClient ? 
            t('admin.categoryUpdatedDescription', { name: data.name || currentCategory.name }) : 
            `${data.name || currentCategory.name} has been updated.`,
        });
      } else {
        // Create new category
        const newCategoryData: Omit<Category, 'id'> = {
          userId: user.uid,
          name: data.name as string,
          icon: data.icon as string,
          itemCount: 0,
          isActive: data.isActive ?? true,
          isVisible: data.isVisible ?? true,
          isFeatured: data.isFeatured ?? false,
          displayOrder: categories.length,
          createdAt: new Date(),
          updatedAt: new Date(),
          ...(data.name_ar ? { name_ar: data.name_ar } : {}),
          ...(data.description ? { description: data.description } : {}),
          ...(data.description_ar ? { description_ar: data.description_ar } : {}),
          ...(data.availableFrom ? { availableFrom: data.availableFrom } : {}),
          ...(data.availableTo ? { availableTo: data.availableTo } : {})
        };
        
        await createCategory(newCategoryData);
        toast({
          title: isClient ? t('admin.categoryCreated') : 'Category created',
          description: isClient ? 
            t('admin.categoryCreatedDescription', { name: data.name as string }) : 
            `${data.name} has been created.`,
        });
      }
      
      // Refresh data
      fetchCategories();
      fetchStats();
      setModalOpen(false);
    } catch (error) {
      console.error('Error saving category:', error);
      toast({
        variant: "destructive",
        title: isClient ? t('admin.errorSavingCategory') : 'Error saving category',
        description: String(error),
      });
    }
  };

  const handleDeleteCategory = async () => {
    if (!currentCategory) return;
    
    try {
      await deleteCategory(currentCategory.id);
      
      toast({
        title: isClient ? t('admin.categoryDeleted') : 'Category deleted',
        description: isClient ? 
          t('admin.categoryDeletedDescription', { name: currentCategory.name }) : 
          `${currentCategory.name} has been deleted.`,
      });
      
      // Refresh data
      fetchCategories();
      fetchStats();
      setDeleteModalOpen(false);
    } catch (error) {
      console.error('Error deleting category:', error);
      toast({
        variant: "destructive",
        title: isClient ? t('admin.errorDeletingCategory') : 'Error deleting category',
        description: String(error),
      });
    }
  };
  
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
          {isClient ? t('admin.navigation.categories') : 'Categories'}
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          {isClient ? t('admin.categoriesDescription') : 'Manage and organize your menu categories'}
        </p>
      </div>
      
      <div className="flex justify-end">
        <button 
          className="bg-[#56999B] dark:bg-[#5DBDC0] text-white px-4 py-2 rounded-lg flex items-center space-x-2 hover:bg-[#74C8CA] dark:hover:bg-[#4A9A9D]"
          onClick={handleAddCategory}
        >
          <i className="fa-solid fa-plus mr-2"></i>
          <span>{isClient ? t('admin.addCategory') : 'Add Category'}</span>
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {isLoading ? (
          // Loading placeholders
          Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6 animate-pulse">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                <div className="flex space-x-2">
                  <div className="w-6 h-6 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  <div className="w-6 h-6 bg-gray-200 dark:bg-gray-700 rounded"></div>
                </div>
              </div>
              <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-2"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
            </div>
          ))
        ) : categories.length > 0 ? (
          // Categories list
          categories.map(category => (
            <div key={category.id} className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-[#A8FDFF] dark:bg-[#5DBDC0]/30 rounded-full flex items-center justify-center">
                  <i className={`fa-solid ${category.icon} text-[#56999B] dark:text-[#5DBDC0] text-xl`}></i>
                </div>
                <div className="flex space-x-2">
                  <button 
                    className="text-gray-400 hover:text-[#56999B] dark:hover:text-[#5DBDC0]"
                    onClick={() => handleEditCategory(category)}
                  >
                    <i className="fa-solid fa-pencil"></i>
                  </button>
                  <button 
                    className="text-gray-400 hover:text-red-500 dark:hover:text-red-400"
                    onClick={() => handleDeleteClick(category)}
                  >
                    <i className="fa-solid fa-trash"></i>
                  </button>
                </div>
              </div>
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                {category.name}
              </h3>
              <p className="text-gray-500 dark:text-gray-400 text-sm mt-1">
                {category.itemCount} {isClient ? t('admin.items') : 'items'}
              </p>
              {(category.availableFrom && category.availableTo) && (
                <div className="mt-4 flex items-center text-sm text-gray-500 dark:text-gray-400">
                  <i className="fa-solid fa-clock mr-2"></i>
                  <span>
                    {isClient ? 
                      t('admin.availableHours', { hours: `${category.availableFrom} - ${category.availableTo}` }) : 
                      `Available: ${category.availableFrom} - ${category.availableTo}`}
                  </span>
                </div>
              )}
            </div>
          ))
        ) : (
          <div className="bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6 col-span-3 text-center">
            <p className="text-gray-500 dark:text-gray-400">
              {isClient ? t('admin.noCategories') : 'No categories found. Add your first category to get started!'}
            </p>
          </div>
        )}

        {/* Add Category Card */}
        <div 
          className="border-2 border-dashed border-[#83EAED] dark:border-[#5DBDC0] rounded-xl p-6 flex flex-col items-center justify-center text-center cursor-pointer hover:bg-[#A8FDFF]/10 dark:hover:bg-[#5DBDC0]/10"
          onClick={handleAddCategory}
        >
          <div className="w-12 h-12 bg-[#A8FDFF] dark:bg-[#5DBDC0]/30 rounded-full flex items-center justify-center mb-4">
            <i className="fa-solid fa-plus text-[#56999B] dark:text-[#5DBDC0] text-xl"></i>
          </div>
          <h3 className="text-lg font-semibold text-[#56999B] dark:text-[#5DBDC0]">
            {isClient ? t('admin.addNewCategory') : 'Add New Category'}
          </h3>
          <p className="text-gray-500 dark:text-gray-400 text-sm mt-1">
            {isClient ? t('admin.clickToAddCategory') : 'Click to add a new menu category'}
          </p>
        </div>
      </div>

      {/* Category Statistics */}
      <div className="mt-8 bg-white dark:bg-[#1d2127] rounded-xl shadow-sm p-6">
        <h2 className="text-xl font-bold text-gray-800 dark:text-white mb-4">
          {isClient ? t('admin.categoryStatistics') : 'Category Statistics'}
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="p-4 bg-[#A8FDFF]/20 dark:bg-[#5DBDC0]/10 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-gray-600 dark:text-gray-400">
                {isClient ? t('admin.totalCategories') : 'Total Categories'}
              </span>
              <span className="text-2xl font-bold text-[#56999B] dark:text-[#5DBDC0]">
                {stats.total}
              </span>
            </div>
          </div>
          <div className="p-4 bg-[#A8FDFF]/20 dark:bg-[#5DBDC0]/10 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-gray-600 dark:text-gray-400">
                {isClient ? t('admin.activeCategories') : 'Active Categories'}
              </span>
              <span className="text-2xl font-bold text-[#56999B] dark:text-[#5DBDC0]">
                {stats.active}
              </span>
            </div>
          </div>
          <div className="p-4 bg-[#A8FDFF]/20 dark:bg-[#5DBDC0]/10 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-gray-600 dark:text-gray-400">
                {isClient ? t('admin.featuredCategories') : 'Featured Categories'}
              </span>
              <span className="text-2xl font-bold text-[#56999B] dark:text-[#5DBDC0]">
                {stats.featured}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Modals */}
      <CategoryModal
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
        category={currentCategory}
        onSave={handleSaveCategory}
        isClient={isClient}
        t={t}
      />
      
      <DeleteConfirmationModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onConfirm={handleDeleteCategory}
        isClient={isClient}
        t={t}
      />
    </div>
  );
} 