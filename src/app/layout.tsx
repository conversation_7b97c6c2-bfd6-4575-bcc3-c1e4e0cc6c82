import type { Metada<PERSON> } from "next";
import "./globals.css";
import { Inter } from "next/font/google";
import { LocaleProvider } from "../contexts/LocaleContext";
import { ThemeProvider } from "../contexts/ThemeContext";
import { PhoneAuthProvider } from "../contexts/PhoneAuthContext";
import { CartProvider } from "../contexts/CartContext";
import LanguageInitializer from "../components/ui/LanguageInitializer";
import { Toaster } from "@/components/ui/toaster";
import ErrorBoundary from "../components/ui/ErrorBoundary";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

export const metadata: Metadata = {
  title: "Barcode Cafe | Digital Menu",
  description: "An interactive digital menu for Barcode Cafe. Savor the Moment, One Bite at a Time.",
  keywords: ["cafe", "menu", "food", "digital menu", "barcode", "restaurant"],
  icons: {
    icon: '/favicon.ico',
    apple: '/favicon.ico',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth" suppressHydrationWarning>
      <head>
        <link 
          rel="stylesheet" 
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" 
          integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" 
          crossOrigin="anonymous" 
          referrerPolicy="no-referrer" 
        />
        {/* Font Awesome JS scripts removed to fix hydration issues */}
      </head>
      <body className={`${inter.variable} antialiased h-full text-base-content`}>
        <ErrorBoundary>
          <ThemeProvider>
            <LocaleProvider>
              <PhoneAuthProvider>
                <CartProvider>
                  <LanguageInitializer />
                  {children}
                  <Toaster />
                </CartProvider>
              </PhoneAuthProvider>
            </LocaleProvider>
          </ThemeProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}