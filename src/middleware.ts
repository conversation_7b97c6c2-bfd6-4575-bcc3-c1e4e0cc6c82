import { NextRequest, NextResponse } from 'next/server';
import { jwtDecode } from 'jwt-decode';

// Custom interface for Firebase JWT payload
interface FirebaseJwtPayload {
  email_verified?: boolean;
  phone_number?: string;
  // Other common Firebase JWT properties
  aud: string;
  auth_time: number;
  exp: number;
  iat: number;
  iss: string;
  sub: string;
  uid: string;
  email?: string;
  name?: string;
  picture?: string;
  // Allow other properties that might be present in the token
  [key: string]: unknown;
}

/**
 * Middleware function to protect routes
 * Runs before the request is completed
 */
export async function middleware(request: NextRequest) {
  // Get the path
  const path = request.nextUrl.pathname;
  
  // Define protected routes
  const isAdminRoute = path.startsWith('/admin');
  const isCustomerRoute = path.startsWith('/customer');
  const isProtectedRoute = isAdminRoute || isCustomerRoute;
  
  // Public routes - always accessible
  const isPublicRoute = path === '/signin' ||
                        path === '/signup' ||
                        path === '/phone-auth' ||
                        path === '/forgot-password' ||
                        path === '/verify-email' ||
                        path.startsWith('/api/auth');
  
  // Check for authentication
  const sessionCookie = request.cookies.get('session')?.value;
  
  // If the route is protected and there's no session, redirect to phone auth
  if (isProtectedRoute && !sessionCookie) {
    const redirectUrl = new URL('/phone-auth', request.url);
    // Add the original url as a "from" param to redirect back after login
    redirectUrl.searchParams.set('from', path);
    return NextResponse.redirect(redirectUrl);
  }
  
  // If there is a session, check email verification for protected routes
  if (isProtectedRoute && sessionCookie) {
    try {
      // Attempt to decode the JWT to get claims - this doesn't verify the signature
      // but extracts the payload which contains the email verification status
      const decoded = jwtDecode<FirebaseJwtPayload>(sessionCookie);
      
      // Check if user is verified (email OR phone)
      const isEmailVerified = decoded.email_verified === true;
      const isPhoneVerified = !!decoded.phone_number; // Phone auth users have phone_number in token

      // If neither email nor phone is verified, redirect to verification page
      if (!isEmailVerified && !isPhoneVerified) {
        return NextResponse.redirect(new URL('/verify-email', request.url));
      }
      
      // For admin routes, we'll rely on client-side checks
      // to determine if the user has admin permissions
    } catch (error) {
      // If token parsing fails, redirect to phone auth as a fallback
      console.error('Token parsing error in middleware:', error);
      return NextResponse.redirect(new URL('/phone-auth', request.url));
    }
  }
  
  // If trying to access login pages while already logged in, redirect appropriately
  // Let auth routes handle the redirect logic based on user type (admin vs regular)
  if (isPublicRoute && sessionCookie && path !== '/verify-email') {
    // Don't redirect here, let the client-side handle this based on user roles
    return NextResponse.next();
  }
  
  // Continue with the request if authentication check passes
  return NextResponse.next();
}

// Define which routes this middleware should run on
export const config = {
  matcher: [
    // Protected routes
    '/admin/:path*',
    '/customer/:path*',
    // Auth routes
    '/signin',
    '/signup',
    '/forgot-password',
    '/verify-email'
  ],
}; 