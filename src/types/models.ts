// Firestore database models

// User Profile Model
export interface UserProfile {
  uid: string;
  displayName: string;
  email: string;
  photoURL?: string;
  phoneNumber?: string;
  createdAt: Date | string;
  updatedAt: Date | string;
  loyaltyPoints: number;
  preferences?: UserPreferences;
  // Enhanced loyalty fields
  loyaltyTier: LoyaltyTier;
  totalPointsEarned: number; // Lifetime points earned
  pointsToNextTier: number; // Points needed for next tier
  achievements: string[]; // Array of achievement IDs
  streakCount: number; // Consecutive order streak
  lastOrderDate?: Date | string; // For streak calculation
  birthday?: Date | string; // For birthday rewards
  favoriteItems: string[]; // Array of menu item IDs
}

// Admin Model
export interface Admin {
  uid: string;
  email: string;
  displayName?: string;
  photoURL?: string;
  createdAt: Date | string;
  updatedAt: Date | string;
}

// User Preferences
export interface UserPreferences {
  language: string;
  darkMode: boolean;
  emailNotifications: boolean;
  pushNotifications: boolean;
}

// Order Model
export interface Order {
  id: string;
  userId: string;
  items: OrderItem[];
  status: OrderStatus;
  subtotal: number;
  deliveryFee: number;
  total: number;
  paymentMethod: PaymentMethod;
  deliveryType?: DeliveryZoneType;
  tableNumber?: string;
  deliveryAddress?: string;
  deliveryZoneId?: string;
  createdAt: Date | string;
  updatedAt: Date | string;
  specialInstructions?: string;
  // Discount fields
  appliedDiscounts?: AppliedDiscount[];
  discountAmount?: number; // Total discount applied
  originalSubtotal?: number; // Subtotal before discounts
  // Cancellation fields
  cancellationData?: OrderCancellationData;
  cancellationHistory?: OrderCancellationAuditEntry[];
  editHistory?: OrderEditAuditEntry[];
}

// Order Cancellation Data
export interface OrderCancellationData {
  reason: string;
  cancelledBy: 'customer' | 'admin';
  cancelledByUserId: string;
  cancelledByEmail?: string;
  cancelledAt: Date | string;
  refundAmount?: number;
  refundMethod?: string;
  notes?: string;
}

// Order Cancellation Audit Entry
export interface OrderCancellationAuditEntry {
  timestamp: Date | string;
  userId: string;
  userEmail: string;
  userType: 'customer' | 'admin';
  action: 'order_cancelled';
  reason: string;
  refundAmount?: number;
  refundMethod?: string;
  notes?: string;
}

// Order Edit Audit Entry
export interface OrderEditAuditEntry {
  timestamp: Date | string;
  adminId: string;
  adminEmail: string;
  action: 'item_added' | 'item_removed' | 'item_modified' | 'delivery_updated' | 'instructions_updated';
  details: string;
  oldValue?: any;
  newValue?: any;
}

// Order Item
export interface OrderItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  options?: OrderItemOption[];
}

// Order Item Option
export interface OrderItemOption {
  name: string;
  value: string;
  priceAdjustment: number;
}

// Order Status
export enum OrderStatus {
  ORDER_PLACED = 'order_placed',
  PREPARING = 'preparing',
  READY_FOR_PICKUP = 'ready_for_pickup',
  OUT_FOR_DELIVERY = 'out_for_delivery',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled'
}

// Payment Method
export enum PaymentMethod {
  CREDIT_CARD = 'credit_card',
  DEBIT_CARD = 'debit_card',
  CASH = 'cash',
  GIFT_CARD = 'gift_card',
  LOYALTY_POINTS = 'loyalty_points'
}

// Address Model
export interface Address {
  id: string;
  userId: string;
  name: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  isDefault: boolean;
  createdAt: Date | string;
  updatedAt: Date | string;
}

// Gift Card Model
export interface GiftCard {
  id: string;
  userId: string;
  code: string;
  initialBalance: number;
  currentBalance: number;
  expiryDate: Date | string;
  isActive: boolean;
  createdAt: Date | string;
  updatedAt: Date | string;
}

// Review Model - Order-Based Review System
export interface Review {
  id: string;
  userId: string;
  orderId: string; // Required - link to specific order
  orderNumber?: string; // For easy reference in admin interface
  customerName?: string; // Customer display name for admin view
  customerEmail?: string; // Customer email for admin reference
  rating: number; // 1-5 stars
  comment?: string; // Optional review comment
  isApproved?: boolean; // Admin moderation status
  adminResponse?: string; // Admin can respond to reviews
  createdAt: Date | string;
  updatedAt: Date | string;
}

// Category Model
export interface Category {
  id: string;
  userId: string;
  name: string;
  name_ar?: string; // Arabic translation for category name
  icon: string;
  description?: string;
  description_ar?: string; // Arabic translation for category description
  itemCount: number;
  availableFrom?: string;
  availableTo?: string;
  isActive: boolean;
  isVisible: boolean;
  isFeatured: boolean;
  displayOrder: number;
  createdAt: Date | string;
  updatedAt: Date | string;
}

// Stock Status
export enum StockStatus {
  IN_STOCK = 'in_stock',
  LOW_STOCK = 'low_stock',
  OUT_OF_STOCK = 'out_of_stock'
}

// Menu Item Model
export interface MenuItem {
  id: string;
  userId: string;
  title: string;
  title_ar?: string; // Arabic translation for item name
  description: string;
  description_ar?: string; // Arabic translation for item description
  price: number;
  categoryId: string;
  image: string;
  stockStatus: StockStatus;
  stockQuantity: number;
  prepTime?: number;
  caffeine?: string;
  caffeine_ar?: string; // Arabic translation for caffeine content
  ingredients?: string;
  ingredients_ar?: string; // Arabic translation for ingredients
  allergens?: string;
  allergens_ar?: string; // Arabic translation for allergens
  isActive: boolean;
  isFeatured: boolean;
  isAvailableForDelivery: boolean;
  createdAt: Date | string;
  updatedAt: Date | string;
}

// Delivery Zone Type
export enum DeliveryZoneType {
  PICK_UP = 'pick_up',
  DELIVERY = 'delivery',
  IN_HOUSE_TABLES = 'in_house_tables'
}

// Delivery Zone Model
export interface DeliveryZone {
  id: string;
  userId: string;
  name: string;
  type: DeliveryZoneType;
  description?: string;
  isActive: boolean;
  // For DELIVERY type
  radius?: number;
  deliveryFee?: number;
  minOrderAmount?: number;
  estimatedDeliveryTime?: number;
  // For IN_HOUSE_TABLES type
  tableNumbers?: string[];
  // Common fields
  createdAt: Date | string;
  updatedAt: Date | string;
}

// Offer Types
export enum OfferType {
  PERCENTAGE = 'percentage', // 10% off
  FIXED_AMOUNT = 'fixed_amount', // SAR 5 off
  FREE_DELIVERY = 'free_delivery', // Free delivery
  BUY_X_GET_Y = 'buy_x_get_y', // Buy 2 get 1 free
  LOYALTY_POINTS = 'loyalty_points', // Loyalty points discount
  PACKAGE_DEAL = 'package_deal' // Combo/Package offers
}

export enum DiscountType {
  ORDER_TOTAL = 'order_total', // Discount on entire order
  CATEGORY = 'category', // Discount on specific category
  MENU_ITEM = 'menu_item', // Discount on specific items
  DELIVERY_FEE = 'delivery_fee' // Discount on delivery fee
}

// Package Item for combo deals
export interface PackageItem {
  menuItemId: string;
  quantity: number;
  isOptional?: boolean; // Customer can choose to exclude
  substitutions?: string[]; // Alternative menu item IDs
}

// Offer Conditions
export interface OfferConditions {
  minOrderAmount?: number; // Minimum order value
  maxDiscountAmount?: number; // Maximum discount cap
  applicableCategories?: string[]; // Category IDs
  applicableMenuItems?: string[]; // Menu item IDs
  deliveryTypesOnly?: DeliveryZoneType[]; // Delivery, pickup, table
  loyaltyPointsRequired?: number; // Minimum loyalty points
  firstOrderOnly?: boolean; // New customers only
  dayOfWeek?: number[]; // 0-6 (Sunday-Saturday)
  timeOfDay?: { start: string; end: string }; // "09:00"-"17:00"

  // Package deal specific conditions
  packageItems?: PackageItem[]; // Items included in package
  packagePrice?: number; // Special package price
  allowCustomization?: boolean; // Allow item substitutions
}

// Main Offer Model
export interface Offer {
  id: string;
  userId: string; // Admin who created it
  name: string;
  name_ar?: string; // Arabic translation
  description: string;
  description_ar?: string; // Arabic translation
  type: OfferType;
  discountType: DiscountType;
  discountValue: number; // Percentage (0-100) or fixed amount

  // Conditions
  conditions: OfferConditions;

  // Validity
  startDate: Date | string;
  endDate: Date | string;
  isActive: boolean;

  // Usage tracking
  usageLimit?: number; // Max total uses
  usageCount: number; // Current usage count
  userUsageLimit?: number; // Max uses per user

  // Metadata
  createdAt: Date | string;
  updatedAt: Date | string;
}

// Applied Discount for cart/order integration
export interface AppliedDiscount {
  offerId: string;
  offerName: string;
  offerType: OfferType;
  discountType: DiscountType;
  discountAmount: number; // Calculated discount amount
  originalAmount?: number; // Original price before discount
  appliedAt: Date | string;
}

// QR Style Model for QR Generation System
export interface QRStyle {
  id: string;
  userId: string; // Admin who created it
  name: string; // User-defined name for the style
  description?: string; // Optional description

  // QR Code Properties
  size: number; // QR code size in pixels
  foregroundColor: string; // Hex color for QR code foreground
  backgroundColor: string; // Hex color for QR code background
  errorCorrectionLevel: 'L' | 'M' | 'Q' | 'H'; // Error correction level
  margin: number; // Margin around QR code

  // Generated QR Data
  qrDataURL: string; // Base64 data URL of generated QR code
  qrSVG: string; // SVG string of generated QR code
  targetURL: string; // URL that QR code points to (menu URL)

  // Usage Statistics
  downloadCount: number; // Number of times downloaded
  printCount: number; // Number of times printed
  lastUsedAt?: Date | string; // Last time this style was used

  // Style Categories
  isTemplate: boolean; // Whether this is a predefined template
  templateCategory?: QRStyleCategory; // Category for templates
  tags?: string[]; // User-defined tags for organization

  // Metadata
  createdAt: Date | string;
  updatedAt: Date | string;
}

// QR Style Categories for templates
export enum QRStyleCategory {
  CLASSIC = 'classic',
  MODERN = 'modern',
  SEASONAL = 'seasonal',
  BRAND = 'brand',
  CUSTOM = 'custom'
}

// ===== LOYALTY SYSTEM MODELS =====

// Loyalty Tier Enum
export enum LoyaltyTier {
  BRONZE = 'bronze',
  SILVER = 'silver',
  GOLD = 'gold',
  PLATINUM = 'platinum'
}

// Loyalty Transaction Types
export enum LoyaltyTransactionType {
  EARNED = 'earned',
  REDEEMED = 'redeemed',
  EXPIRED = 'expired',
  ADJUSTED = 'adjusted', // Manual admin adjustments
  BONUS = 'bonus' // Special bonuses
}

// Loyalty Transaction Source
export enum LoyaltyTransactionSource {
  ORDER = 'order',
  REVIEW = 'review',
  REFERRAL = 'referral',
  BIRTHDAY = 'birthday',
  STREAK = 'streak',
  ACHIEVEMENT = 'achievement',
  RECEIPT_SCAN = 'receipt_scan',
  ADMIN_ADJUSTMENT = 'admin_adjustment',
  REDEMPTION = 'redemption'
}

// Achievement Types
export enum AchievementType {
  ORDER_COUNT = 'order_count',
  TOTAL_SPENT = 'total_spent',
  STREAK = 'streak',
  REVIEW = 'review',
  REFERRAL = 'referral',
  TIER = 'tier',
  SPECIAL = 'special'
}

// Loyalty Transaction Model
export interface LoyaltyTransaction {
  id: string;
  userId: string;
  type: LoyaltyTransactionType;
  source: LoyaltyTransactionSource;
  points: number; // Positive for earned, negative for redeemed
  description: string;
  description_ar?: string; // Arabic translation
  orderId?: string; // If related to an order
  receiptId?: string; // If from receipt scan
  achievementId?: string; // If from achievement
  adminUserId?: string; // If admin adjustment
  expiryDate?: Date | string; // For earned points
  createdAt: Date | string;
  updatedAt: Date | string;
}

// Achievement Model
export interface Achievement {
  id: string;
  name: string;
  name_ar?: string; // Arabic translation
  description: string;
  description_ar?: string; // Arabic translation
  type: AchievementType;
  icon: string; // Icon name or URL
  pointsReward: number;
  tier?: LoyaltyTier; // Required tier to unlock
  isActive: boolean;

  // Achievement conditions
  conditions: {
    orderCount?: number; // Number of orders required
    totalSpent?: number; // Total amount spent required
    streakDays?: number; // Consecutive order days
    reviewCount?: number; // Number of reviews required
    referralCount?: number; // Number of referrals required
    specificItems?: string[]; // Specific menu item IDs
    timeframe?: number; // Days within which to complete
  };

  // Metadata
  createdAt: Date | string;
  updatedAt: Date | string;
}

// Loyalty Rules Configuration
export interface LoyaltyRules {
  id: string;

  // Points earning rules
  pointsPerSAR: number; // Points earned per SAR spent
  bonusMultipliers: {
    [LoyaltyTier.BRONZE]: number;
    [LoyaltyTier.SILVER]: number;
    [LoyaltyTier.GOLD]: number;
    [LoyaltyTier.PLATINUM]: number;
  };

  // Tier thresholds (total points needed)
  tierThresholds: {
    [LoyaltyTier.BRONZE]: number;
    [LoyaltyTier.SILVER]: number;
    [LoyaltyTier.GOLD]: number;
    [LoyaltyTier.PLATINUM]: number;
  };

  // Redemption rules
  pointsToSAR: number; // How many points = 1 SAR
  minRedemptionPoints: number; // Minimum points to redeem
  maxRedemptionPercentage: number; // Max % of order that can be paid with points

  // Point expiry
  pointExpiryDays: number; // Days after which points expire

  // Special bonuses
  firstOrderBonus: number; // Bonus points for first order
  reviewBonus: number; // Points for leaving a review
  birthdayBonus: number; // Birthday bonus points
  streakBonus: number; // Bonus points per consecutive order day

  // Receipt scanning
  receiptScanEnabled: boolean;
  receiptPointsPerSAR: number; // Points per SAR from receipt scan

  // Metadata
  createdAt: Date | string;
  updatedAt: Date | string;
}

// Cashier Receipt Model
export interface CashierReceipt {
  id: string;
  receiptNumber: string; // Unique receipt number
  cashierUserId: string; // Admin/cashier who created it

  // Receipt details
  items: ReceiptItem[];
  subtotal: number;
  tax?: number;
  total: number;
  paymentMethod: PaymentMethod;

  // QR Code data
  qrCode: string; // Unique QR code for this receipt
  qrCodeDataURL: string; // Base64 QR code image

  // Customer info (optional)
  customerName?: string;
  customerPhone?: string;

  // Status
  isScanned: boolean; // Whether customer has scanned for points
  scannedAt?: Date | string;
  scannedByUserId?: string; // User who scanned it
  pointsAwarded?: number; // Points given for this receipt

  // Metadata
  createdAt: Date | string;
  updatedAt: Date | string;
}

// Receipt Item Model
export interface ReceiptItem {
  name: string;
  name_ar?: string;
  price: number;
  quantity: number;
  total: number;
  menuItemId?: string; // Link to menu item if available
}

// Receipt QR Scan Model
export interface ReceiptQRScan {
  id: string;
  userId: string; // Customer who scanned
  receiptId: string; // Receipt that was scanned

  // Scan details
  pointsEarned: number;
  scanLocation?: string; // GPS coordinates or location name
  deviceInfo?: string; // Device information

  // Validation
  isValid: boolean;
  validationErrors?: string[];

  // Metadata
  createdAt: Date | string;
  updatedAt: Date | string;
}

// QR Style Template for predefined styles
export interface QRStyleTemplate {
  id: string;
  name: string;
  name_ar?: string; // Arabic translation
  description: string;
  description_ar?: string; // Arabic translation
  category: QRStyleCategory;
  foregroundColor: string;
  backgroundColor: string;
  size: number;
  isActive: boolean;
  displayOrder: number;
}