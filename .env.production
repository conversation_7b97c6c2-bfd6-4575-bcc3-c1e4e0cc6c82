# Firebase Client Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyBgZ9UTZUfsIxwxIbmAMLspfAwacNvpf6c
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=barcode-qr-menu.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=barcode-qr-menu
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=barcode-qr-menu.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=************
NEXT_PUBLIC_FIREBASE_APP_ID=1:************:web:de23fc4a9804e47300de9b
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=G-Y89R9F99ZV

# Base URL (required for auth actions)
NEXT_PUBLIC_BASE_URL=https://menu.barcode.sa

# Firebase Admin SDK Configuration
FIREBASE_SERVICE_ACCOUNT_KEY={"type":"service_account","project_id":"barcode-qr-menu","private_key_id":"3499e75df953ada84fa154cef595158c9b0427c9","private_key":"-----BEGIN PRIVATE KEY-----\\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDEDluptji0MmkH\\ny2256lS65R8usuFdwILSs+Fn55sFV1ub8LUXST5kGndDYotlMTAQCNB1Fv9Xm4If\\ns1OGoYf5rBVdBV//j0XKjXmhAhX1INS891zRDau2pLwGv887y/BTNr0weabu3i2H\\nQd1eFkf9gdXL1M+o86hdXdU4tS93U45z4Lt98VaIohqUeJl3gkMLqqjwvmxnC4NK\\nphdPzBxQvpBID3wKsCvhmeMOG0mflNFZ/njUwEwJnBYXACbjTPVzEUm5QeNLgL2R\\nSmBIMDFEnXJ8V+cXJml0ANGcoU6+0/PGvXBh2SOwVYFgDEcelgQB+7ZgoBEC1E/F\\newW0/yW9AgMBAAECggEAEWhASDsRzCILrvGnc5He5bHb3mlrLB1LIEpwsX6KsOM2\\n6DYWML6ZPQnChrz0U87rDbQSpMrJb22zFH3Muo/orZ4opBfgPvsUFG4SRgg+4IMC\\nkH1FWZm6oh6nQ9F6T2dbQQWDr7mlZ4uvhCpbbXP8qVatSl9B9CYfepwPB0LDDPeP\\nmQaN5sB3vdESO+QcPiIRFzgSjqWsCCCyggGJ3LNwg1sl3mOAiHpmApCISIwwMnll\\nEsP65SANJNYxJXqoDQkRu5vywEOsNb13OfKLqwPCAii+l1fbCKV3MO+y1ll8e/0d\\n93vpya02qJICqv+RSHcegxkkEZt0CpjB41sIhOs3mwKBgQDqkR6K+OKz15+hxgSK\\nyF9SCmWP9Khey+ab25ikDU/bKlPU5uzNtNk/x6gszMFJ5OUBn5A2NZx0HYnoHgvF\\nQ1Lnx8CLq2YxW2gPxfWxcJsPNrH2FTPDBjIL4I6QXVUbgvCqmFmsSNZvX0Ps0twm\\n3El8boCSFHfpEqIJbDRP+i8oZwKBgQDV+Gl/qSdPSUjroqBegoMJ8AYkpf4LxF1j\\nQ37REZQzTPT0X5nwvyqwfVlknI/aUabdGl/QwfyJY3dY1LB5P8iZNNYGN/ygYz7r\\nXoL9c4p/9yutrGvMswKry9LRwRkk0b7JXR4lgZRXt3ASOvrscEzS6lmS0v5TJ2Wl\\npPkjLUq6OwKBgQCv7RGMR6i4G7N1QK/tauTbAkM0Gdoh5ynZoXS1LerDxNZFKsFW\\ncxoxlMxUdWlOw6bidctSbRkfmXvyJH7Nf3XxssZ9upqV0CmXEvD4GS2i1nHY0AQX\\ndFEayVwDeIy5apaYTFWNOr/hngCy4V8Ook9XTQWPClz46OmwyX/esBSoZwKBgEKn\\n4DWkvFo2ukCtqMfIYIn3+/Huw/VmdrE1lYwLrd9NWgHGxm35H5NbeT2eiDG6vgCi\\n9rZRuBoUollJ6K/c7uDLVOEbD9fnEvK/vo62L68tkK3JyMs7+NkL5eDyBqMxs/2G\\nxJ1qgIvS2VbUiNafJU+PMdhLYm8FFuVDP1l2ApGBAoGAUn0faHBqt2PIqtA6eU1r\\nf4IhlRhxHLHwx1BOM9t7V3aPvOVB31a5TaJ485ibdffKimn25cIZ4V4DVC682xqq\\nnSNAEQSpzIkK5eiWcUS54vZ1IiYhXLGQKgNlzyHrj2TSirPs7l/8gvhJW3u3cBWW\\nV4dHicWVq8JyT0l2A+nVuvc=\\n-----END PRIVATE KEY-----\\n","client_email":"*******","client_id":"118203211214956999146","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40barcode-qr-menu.iam.gserviceaccount.com","universe_domain":"googleapis.com"}

ADMIN_EMAIL=*******
ADMIN_PASSWORD=Admin123!
ADMIN_NAME='Admin User'

FIREBASE_DATABASE_URL=https://barcode-qr-menu.firebaseio.com

# Email Configuration (if using custom verification emails)
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_USER=*******
EMAIL_PASSWORD=password
EMAIL_FROM=*******
