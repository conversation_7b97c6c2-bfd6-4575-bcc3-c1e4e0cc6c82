/// <reference types="node" />
/**
 * Update Collection Ownership Script
 * 
 * This script updates the ownership of menuItems, categories, deliveryZones, and offers
 * from one user ID to another admin user ID in Firestore.
 * 
 * Usage:
 * npm run update:collection-ownership -- --fromUserId=OLD_USER_ID --toUserId=NEW_ADMIN_ID
 */

import * as dotenv from 'dotenv';
// Load from .env.local file
dotenv.config({ path: '.env.local' });

import { initializeApp } from 'firebase/app';
import { 
  getFirestore, 
  collection, 
  query, 
  where, 
  getDocs, 
  writeBatch, 
  doc,
  serverTimestamp 
} from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID
};

// Log Firebase config for debugging (redacting sensitive info)
console.log('Firebase Config:', {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY ? '******' : 'undefined',
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID ? '******' : 'undefined',
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID ? '******' : 'undefined'
});

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Constants
const COLLECTIONS_TO_UPDATE = [
  'menuItems',
  'categories', 
  'deliveryZones',
  'offers'
];

// Default user IDs
const DEFAULT_FROM_USER_ID = 'Iz08frmU7IZhrfS4jW7A0EuGyam2';
const DEFAULT_TO_USER_ID = 'bSVUOClbucRFQeXtsTN7P4Uajz12';

// Get user IDs from command line arguments
function getUserIds(): { fromUserId: string; toUserId: string } {
  const fromUserIdArg = process.argv.find(arg => arg.startsWith('--fromUserId='));
  const toUserIdArg = process.argv.find(arg => arg.startsWith('--toUserId='));
  
  const fromUserId = fromUserIdArg ? fromUserIdArg.split('=')[1] : DEFAULT_FROM_USER_ID;
  const toUserId = toUserIdArg ? toUserIdArg.split('=')[1] : DEFAULT_TO_USER_ID;
  
  return { fromUserId, toUserId };
}

/**
 * Update ownership for a specific collection
 */
async function updateCollectionOwnership(
  collectionName: string, 
  fromUserId: string, 
  toUserId: string
): Promise<number> {
  try {
    console.log(`\n📋 Processing ${collectionName} collection...`);
    
    // Get all documents for the old user ID
    const collectionQuery = query(
      collection(db, collectionName),
      where('userId', '==', fromUserId)
    );
    
    const existingDocs = await getDocs(collectionQuery);
    console.log(`   Found ${existingDocs.size} documents to update`);
    
    if (existingDocs.size === 0) {
      console.log(`   ✅ No documents found for user ${fromUserId} in ${collectionName}`);
      return 0;
    }
    
    // Update documents in batches (Firestore batch limit is 500)
    let updatedCount = 0;
    const docs = existingDocs.docs;
    
    for (let i = 0; i < docs.length; i += 500) {
      const batch = writeBatch(db);
      const batchDocs = docs.slice(i, i + 500);
      
      batchDocs.forEach(docSnapshot => {
        const docRef = doc(db, collectionName, docSnapshot.id);
        batch.update(docRef, { 
          userId: toUserId,
          updatedAt: serverTimestamp()
        });
      });
      
      await batch.commit();
      updatedCount += batchDocs.length;
      console.log(`   Updated ${updatedCount}/${docs.length} documents in ${collectionName}`);
    }
    
    console.log(`   ✅ Successfully updated ${updatedCount} documents in ${collectionName}`);
    return updatedCount;
    
  } catch (error) {
    console.error(`   ❌ Error updating ${collectionName}:`, error);
    throw error;
  }
}

/**
 * Main function to update collection ownership
 */
async function updateCollectionOwnershipMain() {
  try {
    const { fromUserId, toUserId } = getUserIds();
    
    console.log(`🔄 Starting collection ownership update...`);
    console.log(`   From User ID: ${fromUserId}`);
    console.log(`   To User ID: ${toUserId}`);
    console.log(`   Collections: ${COLLECTIONS_TO_UPDATE.join(', ')}`);
    
    // Confirm the operation
    if (fromUserId === toUserId) {
      console.error('❌ Error: From and To user IDs cannot be the same');
      process.exit(1);
    }
    
    let totalUpdated = 0;
    const results: Record<string, number> = {};
    
    // Update each collection
    for (const collectionName of COLLECTIONS_TO_UPDATE) {
      const updatedCount = await updateCollectionOwnership(collectionName, fromUserId, toUserId);
      results[collectionName] = updatedCount;
      totalUpdated += updatedCount;
    }
    
    console.log(`\n✅ Collection ownership update completed successfully!`);
    console.log(`📊 Summary:`);
    for (const [collection, count] of Object.entries(results)) {
      console.log(`   - ${collection}: ${count} documents updated`);
    }
    console.log(`   - Total: ${totalUpdated} documents updated`);
    
    if (totalUpdated === 0) {
      console.log(`\nℹ️ No documents were found for user ${fromUserId} in any collection.`);
    } else {
      console.log(`\n🎉 All collections have been successfully transferred from ${fromUserId} to ${toUserId}!`);
    }
    
  } catch (error) {
    console.error('❌ Error during collection ownership update:', error);
    process.exit(1);
  }
}

// Run the update function
updateCollectionOwnershipMain();
